import { exec } from 'child_process';
import * as path from 'path';
import * as fs from 'fs';
import { BrowserWindow } from 'electron';
import { MainProcessNoticeType } from '../renderer/src/types';

interface ExtractAudioOptions {
  inputPath: string;
  outputPath?: string;
  format: 'mp3' | 'aac' | 'wav' | 'flac' | 'ogg';
  bitrate?: string; // 例如 '128k', '192k', '320k'
  sampleRate?: string; // 例如 '44100', '48000'
  channels?: number; // 1 或 2
}

class AudioExtractor {
  private process: any = null;
  private win: BrowserWindow | null = null;
  private options: ExtractAudioOptions | null = null;

  init(win: BrowserWindow, options: ExtractAudioOptions) {
    this.win = win;
    this.options = options;
    return this;
  }

  async run() {
    if (!this.options || !this.win) {
      this.sendNotice('error', '参数错误', '');
      return;
    }

    const { inputPath, format, bitrate = '192k', sampleRate = '44100', channels = 2 } = this.options;
    
    // 如果没有指定输出路径，则使用输入文件的目录和名称，但更改扩展名
    let outputPath = this.options.outputPath;
    if (!outputPath) {
      const inputDir = path.dirname(inputPath);
      const inputFileName = path.basename(inputPath, path.extname(inputPath));
      outputPath = path.join(inputDir, `${inputFileName}.${format}`);
    }

    // 检查输入文件是否存在
    if (!fs.existsSync(inputPath)) {
      this.sendNotice('error', '输入文件不存在', inputPath);
      return;
    }

    // 构建 ffmpeg 命令
    let command = `ffmpeg -i "${inputPath}" -vn`; // -vn 表示不包含视频流
    
    // 添加音频编码参数
    switch (format) {
      case 'mp3':
        command += ` -c:a libmp3lame -b:a ${bitrate}`;
        break;
      case 'aac':
        command += ` -c:a aac -b:a ${bitrate}`;
        break;
      case 'wav':
        command += ` -c:a pcm_s16le`;
        break;
      case 'flac':
        command += ` -c:a flac`;
        break;
      case 'ogg':
        command += ` -c:a libvorbis -b:a ${bitrate}`;
        break;
    }
    
    // 添加采样率和声道参数
    command += ` -ar ${sampleRate} -ac ${channels}`;
    
    // 添加输出文件路径
    command += ` -y "${outputPath}"`;

    // 发送开始通知
    this.sendNotice('start', '开始提取音频', inputPath);

    try {
      // 执行命令
      this.process = exec(command);
      
      // 监听命令输出
      this.process.stdout.on('data', (data: string) => {
        console.log(`stdout: ${data}`);
      });
      
      this.process.stderr.on('data', (data: string) => {
        console.log(`stderr: ${data}`);
        // 这里可以解析 ffmpeg 的输出来获取进度信息
        // 例如，可以解析 "time=00:00:10.00" 这样的输出来计算进度
        const timeMatch = data.match(/time=(\d+:\d+:\d+\.\d+)/);
        if (timeMatch) {
          const timeStr = timeMatch[1];
          // 将时间字符串转换为秒数
          const [hours, minutes, seconds] = timeStr.split(':').map(parseFloat);
          const totalSeconds = hours * 3600 + minutes * 60 + seconds;
          
          // 发送进度通知
          // 注意：这里需要知道视频总时长才能计算百分比
          // 为简化示例，我们假设已经知道总时长
          // this.sendNotice('progress', totalSeconds, inputPath);
        }
      });
      
      // 监听命令完成
      this.process.on('close', (code: number) => {
        if (code === 0) {
          // 命令成功完成
          this.sendNotice('success', '音频提取完成', outputPath);
        } else {
          // 命令执行失败
          this.sendNotice('error', `音频提取失败，错误代码: ${code}`, inputPath);
        }
        this.process = null;
      });
    } catch (error) {
      this.sendNotice('error', `执行命令时出错: ${error}`, inputPath);
      this.process = null;
    }
  }

  stop() {
    if (this.process) {
      // 在 Windows 上使用 taskkill 命令强制终止进程
      if (process.platform === 'win32') {
        exec(`taskkill /pid ${this.process.pid} /f /t`);
      } else {
        // 在 Unix 系统上使用 kill 命令
        this.process.kill('SIGTERM');
      }
      this.process = null;
      this.sendNotice('stop', '音频提取已停止', this.options?.inputPath || '');
    }
  }

  private sendNotice(type: MainProcessNoticeType, message: string, path: string) {
    if (this.win && !this.win.isDestroyed()) {
      this.win.webContents.send('mainProcessNotice', type, message, path);
    }
  }
}

export default AudioExtractor;
