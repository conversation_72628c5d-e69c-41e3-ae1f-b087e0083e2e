import fs from 'fs'
import path from 'path'
import { spawn } from 'child_process'
import * as log4js from "log4js";
import mime from 'mime';
import ffmpegPath from '@ffmpeg-installer/ffmpeg'
import ffprobePath from '@ffprobe-installer/ffprobe'
import ffmpeg from 'fluent-ffmpeg'
ffmpeg.setFfmpegPath(ffmpegPath.path.replace('app.asar', 'app.asar.unpacked'))
ffmpeg.setFfprobePath(ffprobePath.path.replace('app.asar', 'app.asar.unpacked'))


const logger = log4js.getLogger();

const config = {
  dir: '/Users/<USER>/Downloads/ts_test',
  ffmpeg: '/opt/homebrew/bin/ffmpeg',
  // 是否删除源文件
  rmOriginal: 'no'
}

function getFiles(dir: string): string[] {
  let files

  if (fs.existsSync(dir) && fs.statSync(dir).isDirectory()) {
    files = fs.readdirSync(dir)
    return files
  } else {
    logger.warn('No such directory:', dir)
    process.exit(0)
  }
}

// 检查 ffmpeg
// checkFfmpeg('');

function checkFfmpeg(ffmpeg: string) {
  if (!fs.existsSync(ffmpeg)) {
    logger.warn('Can not find:', ffmpeg);
    process.exit(0);
  } else if (!fs.statSync(ffmpeg).isFile()) {
    logger.warn(`${ffmpeg} is not a file`);
    process.exit(0);
  }
}

export function ts2mp4() {
  const { dir, ffmpeg, rmOriginal } = config
  console.log("%c Line:43 🍋 config", "font-size:18px;color:#ffffff;background:#fca650", config);
  checkFfmpeg(ffmpeg)
  const files = getFiles(dir)
  console.log("%c Line:46 🍫 files", "font-size:18px;color:#ffffff;background:#ed9ec7", files);

  const tsFiles = files
    .filter(file => mime.getType(path.join(dir, file)) === 'video/mp2t')
  console.log("%c Line:49 🍊 tsFiles", "font-size:18px;color:#ffffff;background:#e41a6a", tsFiles);

  const tsFilePaths = tsFiles.map(file => path.join(dir, file))
  console.log("%c Line:52 🎂 tsFilePaths", "font-size:18px;color:#ffffff;background:#33a5ff", tsFilePaths);

  tsFilePaths.reduce((chain, file) =>
    chain.then(_ => {
      const newFileName = /\.ts$/.test(file) ? file.replace('.ts', '.mp4') : `${file}.mp4`
      console.log("%c Line:58 🥐 newFileName", "font-size:18px;color:#ffffff;background:#3f7cff", newFileName);

      return new Promise(resolve => {
        // const convert = spawn(ffmpeg, [
        //   '-i',
        //   file,
        //   '-strict',
        //   '-2',
        //   '-bsf:a',
        //   'aac_adtstoasc',
        //   '-vcodec',
        //   'copy',
        //   newFileName
        // ])

        // convert.stderr.on('data', data => {
        //   console.log(data.toString())
        //   logger.debug(data.toString())
        // })

        // convert.on('close', code => {
        //   console.log(`${file} conversion process exited with code ${code}`)
        //   logger.debug(`${file} conversion process exited with code ${code}`)
        //   rmOriginal === 'yes' && fs.unlinkSync(file)
        //   resolve(0)
        // })
      })
    })
    , Promise.resolve(0))

}

function doTs2mp4(filePath, rmOriginal:string) {
  const newFileName = /\.ts$/.test(filePath) ? filePath.replace('.ts', '.mp4') : `${filePath}.mp4`;

  return new Promise((resolve, reject) => {
    ffmpeg(filePath)
      .output(newFileName)
      .audioCodec('aac')
      .videoCodec('copy')
      .addOption('-strict', '-2')
      .addOption('-bsf:a', 'aac_adtstoasc')
      .on('error', (err) => {
        console.error(`Error during conversion: ${err.message}`);
        logger.error(`Error during conversion: ${err.message}`);
        reject(err);
      })
      .on('end', () => {
        console.log(`${filePath} has been converted to ${newFileName}`);
        logger.debug(`${filePath} has been converted to ${newFileName}`);
        rmOriginal === 'yes' && fs.unlinkSync(filePath);
        resolve(0);
      })
      .run();
  });
}
