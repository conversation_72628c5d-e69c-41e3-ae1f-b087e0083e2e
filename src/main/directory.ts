import { dialog } from 'electron'
import { <PERSON>rowserWindow } from 'electron/main'

export const selectDirectory = async () => {
  const res = await dialog.showOpenDialog({
    title: '选择文件夹',
    properties: ['openDirectory', 'createDirectory']
  })
  return res.canceled === false ? res.filePaths[0] : ''
}


export const selectFile = async (win: BrowserWindow, fileType: 'video' | 'image' | 'all' = 'all', multiple: boolean = true) => {
  let filters: { name: string; extensions: string[] }[] = [];

  switch (fileType) {
    case 'video':
      filters = [
        { name: '视频文件', extensions: ['mp4', 'mkv', 'avi', 'mov', 'webm', 'flv', 'ts'] }
      ];
      break;
    case 'image':
      filters = [
        { name: '图片文件', extensions: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'] }
      ];
      break;
    case 'all':
    default:
      filters = [
        { name: '媒体文件', extensions: ['mp4', 'mkv', 'avi', 'mov', 'webm', 'flv', 'jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'] },
        { name: '视频文件', extensions: ['mp4', 'mkv', 'avi', 'mov', 'webm', 'flv', 'ts'] },
        { name: '图片文件', extensions: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'] }
      ];
      break;
  }

  // 根据 multiple 参数决定是否允许多选
  const properties: Array<'openFile' | 'openDirectory' | 'multiSelections' | 'showHiddenFiles' | 'createDirectory' | 'promptToCreate' | 'noResolveAliases' | 'treatPackageAsDirectory' | 'dontAddToRecent'> = multiple
    ? ['openFile', 'multiSelections']
    : ['openFile']

  const res = await dialog.showOpenDialog({
    title: '选择文件',
    properties,
    filters
  });

  // 在 Electron 中，即使只设置了 'openFile'，用户仍然可以通过按住 Ctrl/Cmd 键来选择多个文件。要严格限制为单选，我们需要在对话框关闭后额外处理返回的结果。
  // 如果是单选模式，只返回第一个选择的文件
  if (!multiple && res.filePaths.length > 0) {
    return res.canceled ? '' : [res.filePaths[0]];
  }
  return res.canceled === false ? res.filePaths : '';
}
