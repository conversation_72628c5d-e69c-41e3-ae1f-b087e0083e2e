import { exec } from 'child_process';
import { promisify } from 'util';
import * as fs from 'fs';
import * as path from 'path';

const execPromise = promisify(exec);

/**
 * 获取视频文件信息
 * @param filePath 视频文件路径
 * @returns 视频文件信息
 */
export const getVideoInfo = async (filePath: string) => {
	try {
		// 检查文件是否存在
		if (!fs.existsSync(filePath)) {
			throw new Error('文件不存在');
		}

		// 检查文件是否为视频文件
		const ext = path.extname(filePath).toLowerCase().slice(1);
		const videoExtensions = ['mp4', 'avi', 'mkv', 'mov', 'webm', 'flv', 'ts', 'mts', 'm2ts', 'wmv', 'asf', '3gp', 'm4v'];
		if (!videoExtensions.includes(ext)) {
			throw new Error('不是支持的视频文件格式');
		}

		// 使用 ffprobe 获取视频信息
		// 注意：这里假设系统中已经安装了 ffprobe
		// 在实际应用中，你可能需要使用 ffprobe 的绝对路径或者使用 fluent-ffmpeg 等库
		const command = `ffprobe -v quiet -print_format json -show_format -show_streams "${filePath}"`;
		const { stdout } = await execPromise(command);

		// 解析 JSON 输出
		const info = JSON.parse(stdout);

		// 返回视频信息
		return info;
	} catch (error) {
		console.error('获取视频信息失败:', error);
		throw error;
	}
};

/**
 * 模拟获取视频信息（用于开发和测试）
 * @param filePath 视频文件路径
 * @returns 模拟的视频信息
 */
export const simulateVideoInfo = (filePath: string) => {
	// 从文件路径中提取文件名
	const filename = path.basename(filePath);
	const fileExt = path.extname(filePath).toLowerCase().slice(1);

	// 生成随机视频信息
	const randomDuration = Math.floor(Math.random() * 3600);
	const minutes = Math.floor(randomDuration / 60);
	const seconds = randomDuration % 60;
	const duration = `${minutes}:${seconds.toString().padStart(2, '0')}`;

	const randomSize = Math.floor(Math.random() * 1000) + 10;
	const randomBitrate = Math.floor(Math.random() * 10000) + 1000;

	const randomWidth = [1280, 1920, 3840][Math.floor(Math.random() * 3)];
	const randomHeight = randomWidth === 1280 ? 720 : randomWidth === 1920 ? 1080 : 2160;
	const randomFrameRate = [24, 30, 60][Math.floor(Math.random() * 3)];

	// 根据文件扩展名确定容器格式
	let formatName = 'unknown';
	let formatLongName = 'Unknown Format';

	switch (fileExt) {
		case 'mp4':
			formatName = 'mp4';
			formatLongName = 'MP4 (MPEG-4 Part 14)';
			break;
		case 'mkv':
			formatName = 'matroska';
			formatLongName = 'Matroska / WebM';
			break;
		case 'avi':
			formatName = 'avi';
			formatLongName = 'AVI (Audio Video Interleaved)';
			break;
		case 'mov':
			formatName = 'mov';
			formatLongName = 'QuickTime / MOV';
			break;
		case 'webm':
			formatName = 'webm';
			formatLongName = 'WebM';
			break;
		case 'flv':
			formatName = 'flv';
			formatLongName = 'FLV (Flash Video)';
			break;
		case 'ts':
		case 'mts':
		case 'm2ts':
			formatName = 'mpegts';
			formatLongName = 'MPEG-TS (MPEG-2 Transport Stream)';
			break;
		case 'wmv':
			formatName = 'asf';
			formatLongName = 'Windows Media Video';
			break;
		default:
			formatName = fileExt;
			formatLongName = fileExt.toUpperCase() + ' Format';
	}

	// 随机选择视频编解码器
	const videoCodecs = [
		{ name: 'h264', long_name: 'H.264 / AVC / MPEG-4 AVC / MPEG-4 part 10', profile: 'High' },
		{ name: 'hevc', long_name: 'H.265 / HEVC (High Efficiency Video Coding)', profile: 'Main' },
		{ name: 'vp9', long_name: 'Google VP9', profile: 'Profile 0' },
		{ name: 'av1', long_name: 'AOMedia Video 1', profile: 'Main' },
		{ name: 'mpeg4', long_name: 'MPEG-4 part 2', profile: 'Advanced Simple Profile' }
	];

	// 随机选择音频编解码器
	const audioCodecs = [
		{ name: 'aac', long_name: 'AAC (Advanced Audio Coding)', profile: 'LC' },
		{ name: 'mp3', long_name: 'MP3 (MPEG audio layer 3)', profile: '' },
		{ name: 'opus', long_name: 'Opus (Opus Interactive Audio Codec)', profile: '' },
		{ name: 'flac', long_name: 'FLAC (Free Lossless Audio Codec)', profile: '' },
		{ name: 'vorbis', long_name: 'Vorbis', profile: '' }
	];

	const selectedVideoCodec = videoCodecs[Math.floor(Math.random() * videoCodecs.length)];
	const selectedAudioCodec = audioCodecs[Math.floor(Math.random() * audioCodecs.length)];

	// 生成随机像素格式
	const pixFormats = ['yuv420p', 'yuv422p', 'yuv444p', 'yuvj420p', 'rgb24', 'bgr24'];
	const randomPixFmt = pixFormats[Math.floor(Math.random() * pixFormats.length)];

	// 生成随机色彩空间
	const colorSpaces = ['bt709', 'bt470bg', 'smpte170m', 'bt2020nc'];
	const randomColorSpace = colorSpaces[Math.floor(Math.random() * colorSpaces.length)];

	// 生成随机色彩范围
	const colorRanges = ['tv', 'pc', 'limited', 'full'];
	const randomColorRange = colorRanges[Math.floor(Math.random() * colorRanges.length)];

	// 生成随机音频采样格式
	const sampleFmts = ['fltp', 's16', 's16p', 's32p', 'flt'];
	const randomSampleFmt = sampleFmts[Math.floor(Math.random() * sampleFmts.length)];

	// 生成随机元数据
	const tags = {
		title: Math.random() > 0.5 ? `Sample Video ${Math.floor(Math.random() * 100)}` : undefined,
		artist: Math.random() > 0.7 ? `Artist ${Math.floor(Math.random() * 10)}` : undefined,
		date: Math.random() > 0.6 ? new Date().toISOString().split('T')[0] : undefined,
		encoder: Math.random() > 0.5 ? `Encoder ${Math.floor(Math.random() * 5)}` : undefined
	};

	// 过滤掉 undefined 的标签
	const filteredTags = Object.fromEntries(
		Object.entries(tags).filter(([_, v]) => v !== undefined)
	);

	return {
		format: {
			filename,
			format_name: formatName,
			format_long_name: formatLongName,
			duration: duration,
			size: `${randomSize} MB`,
			bit_rate: `${randomBitrate} kb/s`,
			nb_streams: 2,
			tags: Object.keys(filteredTags).length > 0 ? filteredTags : undefined
		},
		streams: [
			{
				codec_type: 'video',
				codec_name: selectedVideoCodec.name,
				codec_long_name: selectedVideoCodec.long_name,
				profile: selectedVideoCodec.profile,
				width: randomWidth,
				height: randomHeight,
				coded_width: randomWidth,
				coded_height: randomHeight,
				display_aspect_ratio: `${16}:${9}`,
				pix_fmt: randomPixFmt,
				level: Math.floor(Math.random() * 52) / 10,
				color_range: randomColorRange,
				color_space: randomColorSpace,
				r_frame_rate: `${randomFrameRate}/1`,
				avg_frame_rate: `${randomFrameRate}/1`,
				time_base: '1/90000',
				bit_rate: `${randomBitrate} kb/s`,
				nb_frames: `${Math.floor(randomDuration * randomFrameRate)}`
			},
			{
				codec_type: 'audio',
				codec_name: selectedAudioCodec.name,
				codec_long_name: selectedAudioCodec.long_name,
				profile: selectedAudioCodec.profile || undefined,
				channels: 2,
				channel_layout: 'stereo',
				sample_fmt: randomSampleFmt,
				sample_rate: '48000',
				bit_rate: `${192} kb/s`,
				time_base: '1/48000'
			}
		]
	};
};
