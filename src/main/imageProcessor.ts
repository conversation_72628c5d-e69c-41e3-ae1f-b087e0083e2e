import { BrowserWindow } from 'electron';
import * as fs from 'fs';
import * as path from 'path';
import sharp from 'sharp';
import { MainProcessNoticeType } from '../renderer/src/types';
import { SystemNotification } from './notification';

/**
 * 图片处理工具类
 */
export class ImageProcessor {
	private window: BrowserWindow | null = null;
	private isProcessing: boolean = false;
	private shouldStop: boolean = false;

	/**
	 * 初始化图片处理器
	 * @param win Electron 窗口实例
	 */
	constructor(win: BrowserWindow) {
		this.window = win;
	}

	/**
	 * 压缩图片
	 * @param options 压缩选项
	 * @returns 压缩结果
	 */
	async compressImage(options: {
		inputPath: string;
		outputPath?: string;
		quality: number; // 1-100
		width?: number;
		height?: number;
		format?: string; // 输出格式，默认与输入相同
		deleteOriginal?: boolean; // 压缩完成后是否删除原图片
	}) {
		const { inputPath, quality, width, height, format, deleteOriginal = false } = options;

		// 如果没有指定输出路径，则使用输入文件的目录和名称，添加后缀
		let outputPath = options.outputPath;
		if (!outputPath) {
			const inputDir = path.dirname(inputPath);
			const inputFileName = path.basename(inputPath, path.extname(inputPath));
			const outputExt = format ? `.${format}` : path.extname(inputPath);
			outputPath = path.join(inputDir, `${inputFileName}_compressed${outputExt}`);
		}

		// 检查输入文件是否存在
		if (!fs.existsSync(inputPath)) {
			this.sendNotice(MainProcessNoticeType.ERROR, '输入文件不存在', inputPath);
			throw new Error('输入文件不存在');
		}

		// 发送开始通知
		this.sendNotice(MainProcessNoticeType.START, '开始压缩图片', inputPath);
		this.isProcessing = true;
		this.shouldStop = false;

		try {
			// 读取图片
			let image = sharp(inputPath);

			// 获取图片信息
			const metadata = await image.metadata();

			// 调整大小（如果指定了宽度或高度）
			if (width || height) {
				image = image.resize({
					width,
					height,
					fit: 'inside',
					withoutEnlargement: true
				});
			}

			// 设置输出格式和质量
			if (format) {
				switch (format.toLowerCase()) {
					case 'jpeg':
					case 'jpg':
						image = image.jpeg({ quality });
						break;
					case 'png':
						image = image.png({ quality: Math.max(1, Math.floor(quality / 10)) });
						break;
					case 'webp':
						image = image.webp({ quality });
						break;
					case 'avif':
						image = image.avif({ quality });
						break;
					default:
						// 默认使用 JPEG
						image = image.jpeg({ quality });
				}
			} else {
				// 根据原始格式设置质量
				const originalFormat = metadata.format;
				switch (originalFormat) {
					case 'jpeg':
						image = image.jpeg({ quality });
						break;
					case 'png':
						image = image.png({ quality: Math.max(1, Math.floor(quality / 10)) });
						break;
					case 'webp':
						image = image.webp({ quality });
						break;
					case 'avif':
						image = image.avif({ quality });
						break;
					default:
						// 默认使用 JPEG
						image = image.jpeg({ quality });
				}
			}

			// 检查是否应该停止处理
			if (this.shouldStop) {
				this.isProcessing = false;
				this.sendNotice(MainProcessNoticeType.STOP, '图片压缩已停止', inputPath);
				return null;
			}

			// 保存图片
			await image.toFile(outputPath);

			// 发送完成通知
			this.sendNotice(MainProcessNoticeType.SUCCESS, '图片压缩完成', outputPath);
			this.isProcessing = false;

			// 如果需要删除原图片
			if (deleteOriginal && fs.existsSync(inputPath)) {
				try {
					fs.unlinkSync(inputPath);
					this.sendNotice(MainProcessNoticeType.SUCCESS, '原图片已删除', inputPath);
				} catch (error: unknown) {
					console.error('删除原图片失败:', error);
					this.sendNotice(MainProcessNoticeType.ERROR, `删除原图片失败: ${error instanceof Error ? error.message : String(error)}`, inputPath);
				}
			}

			return outputPath;
		} catch (error) {
			this.isProcessing = false;
			this.sendNotice(MainProcessNoticeType.ERROR, `图片压缩失败: ${(error as Error).message}`, inputPath);
			throw error;
		}
	}

	/**
	 * 转换图片格式
	 * @param options 转换选项
	 * @returns 转换结果
	 */
	async convertImageFormat(options: {
		inputPath: string;
		outputPath?: string;
		format: 'jpeg' | 'png' | 'webp' | 'avif' | 'tiff' | 'gif';
		quality?: number; // 1-100
	}) {
		const { inputPath, format, quality = 80 } = options;

		// 如果没有指定输出路径，则使用输入文件的目录和名称，但更改扩展名
		let outputPath = options.outputPath;
		if (!outputPath) {
			const inputDir = path.dirname(inputPath);
			const inputFileName = path.basename(inputPath, path.extname(inputPath));
			outputPath = path.join(inputDir, `${inputFileName}.${format}`);
		}

		// 检查输入文件是否存在
		if (!fs.existsSync(inputPath)) {
			this.sendNotice(MainProcessNoticeType.ERROR, '输入文件不存在', inputPath);
			throw new Error('输入文件不存在');
		}

		// 发送开始通知
		this.sendNotice(MainProcessNoticeType.START, '开始转换图片格式', inputPath);
		this.isProcessing = true;
		this.shouldStop = false;

		try {
			// 读取图片
			let image = sharp(inputPath);

			// 设置输出格式和质量
			switch (format.toLowerCase()) {
				case 'jpeg':
				case 'jpg':
					image = image.jpeg({ quality });
					break;
				case 'png':
					image = image.png({ quality: Math.max(1, Math.floor(quality / 10)) });
					break;
				case 'webp':
					image = image.webp({ quality });
					break;
				case 'avif':
					image = image.avif({ quality });
					break;
				case 'tiff':
					image = image.tiff({ quality });
					break;
				case 'gif':
					// Sharp 不支持直接输出 GIF，但我们可以保留这个选项
					image = image.png();
					break;
				default:
					// 默认使用 JPEG
					image = image.jpeg({ quality });
			}

			// 检查是否应该停止处理
			if (this.shouldStop) {
				this.isProcessing = false;
				this.sendNotice(MainProcessNoticeType.STOP, '图片格式转换已停止', inputPath);
				return null;
			}

			// 保存图片
			await image.toFile(outputPath);

			// 发送完成通知
			this.sendNotice(MainProcessNoticeType.SUCCESS, '图片格式转换完成', outputPath);
			this.isProcessing = false;

			return outputPath;
		} catch (error) {
			this.isProcessing = false;
			this.sendNotice(MainProcessNoticeType.ERROR, `图片格式转换失败: ${(error as Error).message}`, inputPath);
			throw error;
		}
	}

	/**
	 * 提升图片清晰度
	 * @param options 清晰度提升选项
	 * @returns 处理结果
	 */
	async enhanceImage(options: {
		inputPath: string;
		outputPath?: string;
		scale: number; // 放大倍数
		sharpness: number; // 锐化程度 (0-100)
		denoise: boolean; // 是否降噪
		denoiseLevel?: number; // 降噪级别 (0-100)
		format?: string; // 输出格式，默认与输入相同
		quality?: number; // 输出质量 (1-100)
		deleteOriginal?: boolean; // 处理完成后是否删除原图片
	}) {
		const {
			inputPath,
			scale,
			sharpness,
			denoise,
			denoiseLevel = 50,
			format,
			quality = 90,
			deleteOriginal = false
		} = options;

		// 如果没有指定输出路径，则使用输入文件的目录和名称，添加后缀
		let outputPath = options.outputPath;
		if (!outputPath) {
			const inputDir = path.dirname(inputPath);
			const inputFileName = path.basename(inputPath, path.extname(inputPath));
			const outputExt = format ? `.${format}` : path.extname(inputPath);
			outputPath = path.join(inputDir, `${inputFileName}_enhanced${outputExt}`);
		}

		// 检查输入文件是否存在
		if (!fs.existsSync(inputPath)) {
			this.sendNotice(MainProcessNoticeType.ERROR, '输入文件不存在', inputPath);
			throw new Error('输入文件不存在');
		}

		// 发送开始通知
		this.sendNotice(MainProcessNoticeType.START, '开始提升图片清晰度', inputPath);
		this.isProcessing = true;
		this.shouldStop = false;

		try {
			// 读取图片
			let image = sharp(inputPath);

			// 获取图片信息
			const metadata = await image.metadata();

			// 计算新的尺寸
			const newWidth = metadata.width ? Math.round(metadata.width * scale) : undefined;
			const newHeight = metadata.height ? Math.round(metadata.height * scale) : undefined;

			// 放大图片
			image = image.resize({
				width: newWidth,
				height: newHeight,
				fit: 'fill', // 保持宽高比
				kernel: 'lanczos3' // 使用高质量的重采样算法
			});

			// 应用降噪（如果启用）
			if (denoise) {
				// Sharp 没有直接的降噪功能，但可以通过轻微模糊然后锐化来模拟
				// 降噪级别越高，模糊半径越大
				const blurRadius = Math.max(0.3, Math.min(2.0, denoiseLevel / 50));
				image = image.blur(blurRadius);
			}

			// 应用锐化
			// 锐化参数：sigma 值越小，锐化效果越强
			const sigma = Math.max(0.3, 1.5 - (sharpness / 100));
			image = image.sharpen({
				sigma,
				m1: 0,
				m2: 20,
				x1: 10,
				y2: 40,
				y3: 20
			});

			// 设置输出格式和质量
			if (format) {
				switch (format.toLowerCase()) {
					case 'jpeg':
					case 'jpg':
						image = image.jpeg({ quality });
						break;
					case 'png':
						image = image.png({ quality: Math.max(1, Math.floor(quality / 10)) });
						break;
					case 'webp':
						image = image.webp({ quality });
						break;
					case 'avif':
						image = image.avif({ quality });
						break;
					default:
						// 默认使用 JPEG
						image = image.jpeg({ quality });
				}
			} else {
				// 根据原始格式设置质量
				const originalFormat = metadata.format;
				switch (originalFormat) {
					case 'jpeg':
						image = image.jpeg({ quality });
						break;
					case 'png':
						image = image.png({ quality: Math.max(1, Math.floor(quality / 10)) });
						break;
					case 'webp':
						image = image.webp({ quality });
						break;
					case 'avif':
						image = image.avif({ quality });
						break;
					default:
						// 默认使用 JPEG
						image = image.jpeg({ quality });
				}
			}

			// 检查是否应该停止处理
			if (this.shouldStop) {
				this.isProcessing = false;
				this.sendNotice(MainProcessNoticeType.STOP, '图片清晰度提升已停止', inputPath);
				return null;
			}

			// 保存图片
			await image.toFile(outputPath);

			// 发送完成通知
			this.sendNotice(MainProcessNoticeType.SUCCESS, '图片清晰度提升完成', outputPath);
			this.isProcessing = false;

			// 如果需要删除原图片
			if (deleteOriginal && fs.existsSync(inputPath)) {
				try {
					fs.unlinkSync(inputPath);
					this.sendNotice(MainProcessNoticeType.SUCCESS, '原图片已删除', inputPath);
				} catch (error: unknown) {
					console.error('删除原图片失败:', error);
					this.sendNotice(MainProcessNoticeType.ERROR, `删除原图片失败: ${error instanceof Error ? error.message : String(error)}`, inputPath);
				}
			}

			return outputPath;
		} catch (error) {
			this.isProcessing = false;
			this.sendNotice(MainProcessNoticeType.ERROR, `图片清晰度提升失败: ${(error as Error).message}`, inputPath);
			throw error;
		}
	}

	/**
	 * 停止当前处理
	 */
	stop() {
		if (this.isProcessing) {
			this.shouldStop = true;
			this.sendNotice(MainProcessNoticeType.STOP, '处理已停止', '');
		}
	}

	/**
	 * 发送通知到渲染进程
	 * @param type 通知类型
	 * @param data 通知数据
	 * @param path 文件路径
	 */
	private sendNotice(type: MainProcessNoticeType, data: any, path: string) {
		if (this.window && !this.window.isDestroyed()) {
			this.window.webContents.send('mainProcessNotice', type, data, path);

			// 对于成功完成的操作，发送系统通知
			if (type === MainProcessNoticeType.SUCCESS) {
				const fileName = path ? this.getFileName(path) : '';
				SystemNotification.show('操作完成', `${data} ${fileName}`);
			}
		}
	}

	/**
	 * 获取文件名
	 * @param filePath 文件路径
	 * @returns 文件名
	 */
	private getFileName(filePath: string): string {
		if (!filePath) return '';
		// 处理不同操作系统的路径分隔符
		const normalizedPath = filePath.replace(/\\/g, '/');
		return normalizedPath.split('/').pop() || filePath;
	}
}
