import { Notification } from 'electron';

/**
 * 系统通知工具类
 */
export class SystemNotification {
  /**
   * 显示系统通知
   * @param title 通知标题
   * @param body 通知内容
   */
  static show(title: string, body: string): void {
    try {
      const notification = new Notification({
        title,
        body,
        silent: false
      });
      
      notification.show();
    } catch (error) {
      console.error('显示系统通知失败:', error);
    }
  }
}
