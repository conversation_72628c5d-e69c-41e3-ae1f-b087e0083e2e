import { createFileRoute } from '@tanstack/react-router'
import { Link } from '@tanstack/react-router'
import { getToolsByCategory } from '@renderer/config/routes'

export const Route = createFileRoute('/')({
	component: Index
})

function Index() {
	// 获取各类工具并转换为卡片格式
	const getToolCards = (category: 'video' | 'image' | 'dev' | 'text') => {
		return getToolsByCategory(category).map(tool => {
			const Icon = tool.icon;
			return {
				name: tool.title,
				description: tool.description,
				path: tool.path,
				icon: <Icon className="w-8 h-8" style={{ color: tool.color || '#1890FF' }} />
			};
		});
	}

	// 获取各类工具卡片
	const videoTools = getToolCards('video')
	const imageTools = getToolCards('image')
	const devTools = getToolCards('dev')
	const textTools = getToolCards('text')

	// 定义工具卡片类型
	interface ToolCard {
		name: string;
		description: string;
		path: string;
		icon: React.ReactNode;
	}

	// 创建一个渲染工具卡片的函数
	const renderToolCards = (tools: ToolCard[], title: string) => (
		<div className="mb-8">
			<h3 className="text-[#595959] text-lg font-medium mb-4">{title}</h3>
			<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
				{tools.map((tool, index) => (
					<Link
						key={index}
						to={tool.path}
						className="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow flex flex-col items-center text-center"
					>
						<div className="mb-4">{tool.icon}</div>
						<h4 className="text-[#262626] font-medium mb-1">{tool.name}</h4>
						<p className="text-[#8C8C8C] text-sm">{tool.description}</p>
					</Link>
				))}
			</div>
		</div>
	);

	// 所有工具卡片数据已经在上面创建

	return (
		<div className="flex flex-col min-h-0 h-full">
			<div className="flex-none">
				<h2 className="text-[#262626] text-xl font-medium">欢迎使用 Media Tool</h2>
				<p className="text-[#8C8C8C] text-sm mt-1">一站式媒体处理工具，轻松处理视频、图片和文本</p>
			</div>

			<div className="flex-1 mt-6 min-h-0">
				<div className="h-full overflow-auto">
					<div className="space-y-4">
						{renderToolCards(videoTools, "视频工具")}
						{renderToolCards(imageTools, "图片工具")}
						{renderToolCards(devTools, "开发工具")}
						{renderToolCards(textTools, "文本工具")}
					</div>
				</div>
			</div>
		</div>
	)
}
