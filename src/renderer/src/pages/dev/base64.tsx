import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent } from '@/components/ui/card'
import { Copy, Hash, Upload, FileImage } from 'lucide-react'
import { toast } from 'sonner'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

export const Route = createFileRoute('/dev/base64')({
	component: Base64Tool
})

function Base64Tool() {
	const [textInput, setTextInput] = useState('')
	const [textOutput, setTextOutput] = useState('')
	const [imagePreview, setImagePreview] = useState<string | null>(null)
	const [activeTab, setActiveTab] = useState('text')

	const encodeBase64 = () => {
		try {
			if (!textInput.trim()) {
				setTextOutput('')
				toast.error('请输入要编码的文本')
				return
			}

			const encoded = btoa(unescape(encodeURIComponent(textInput)))
			setTextOutput(encoded)
			toast.success('Base64 编码成功')
		} catch (err) {
			toast.error(`编码错误: ${err instanceof Error ? err.message : String(err)}`)
		}
	}

	const decodeBase64 = () => {
		try {
			if (!textInput.trim()) {
				setTextOutput('')
				toast.error('请输入要解码的 Base64 字符串')
				return
			}

			const decoded = decodeURIComponent(escape(atob(textInput)))
			setTextOutput(decoded)
			toast.success('Base64 解码成功')
		} catch (err) {
			toast.error(`解码错误: ${err instanceof Error ? err.message : String(err)}`)
		}
	}

	const copyToClipboard = () => {
		if (!textOutput) {
			toast.error('没有可复制的内容')
			return
		}

		navigator.clipboard.writeText(textOutput)
			.then(() => toast.success('已复制到剪贴板'))
			.catch(() => toast.error('复制失败'))
	}

	const handleTextFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0]
		if (!file) return

		const reader = new FileReader()
		reader.onload = (e) => {
			const content = e.target?.result as string
			setTextInput(content)
		}
		reader.onerror = () => {
			toast.error('文件读取失败')
		}
		reader.readAsText(file)
	}

	const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0]
		if (!file) return

		if (!file.type.startsWith('image/')) {
			toast.error('请上传图片文件')
			return
		}

		const reader = new FileReader()
		reader.onload = (e) => {
			const base64String = e.target?.result as string
			setTextInput(base64String.split(',')[1] || '')
			setImagePreview(base64String)
		}
		reader.onerror = () => {
			toast.error('图片读取失败')
		}
		reader.readAsDataURL(file)
	}

	const handleImageDecode = () => {
		try {
			if (!textInput.trim()) {
				setImagePreview(null)
				toast.error('请输入要解码的 Base64 图片字符串')
				return
			}

			// 检查是否已经包含 data:image 前缀
			const base64String = textInput.startsWith('data:image')
				? textInput
				: `data:image/png;base64,${textInput}`

			setImagePreview(base64String)
			toast.success('图片解码成功')
		} catch (err) {
			toast.error(`图片解码错误: ${err instanceof Error ? err.message : String(err)}`)
		}
	}

	return (
		<div className="flex flex-col min-h-0 h-full">
			<div className="flex-none">
				<h2 className="text-[#262626] text-xl font-medium">Base64 编解码</h2>
				<p className="text-[#8C8C8C] text-sm mt-1">对文本或图片进行 Base64 编码和解码</p>
			</div>

			<div className="flex-1 mt-6 min-h-0">
				<div className="h-full bg-white rounded-lg p-6">
					<Tabs defaultValue="text" className="h-full flex flex-col" onValueChange={setActiveTab}>
						<TabsList className="mb-4 flex-none">
							<TabsTrigger value="text">文本编解码</TabsTrigger>
							<TabsTrigger value="image">图片编解码</TabsTrigger>
						</TabsList>

						<TabsContent value="text" className="flex-1 overflow-hidden">
							<div className="grid grid-cols-1 md:grid-cols-2 gap-6 h-full">
								<Card className="flex flex-col h-full">
									<CardContent className="flex flex-col h-full p-4">
										<div className="flex justify-between items-center mb-2">
											<h3 className="text-base font-medium flex items-center">
												<Hash className="w-4 h-4 mr-2" />
												输入文本
											</h3>
											<div>
												<input
													type="file"
													onChange={handleTextFileUpload}
													className="hidden"
													id="text-file-input"
												/>
												<label htmlFor="text-file-input">
													<Button variant="outline" size="sm" className="cursor-pointer" asChild>
														<span>
															<Upload className="w-4 h-4 mr-1" />
															上传文件
														</span>
													</Button>
												</label>
											</div>
										</div>
										<Textarea
											value={textInput}
											onChange={(e) => setTextInput(e.target.value)}
											placeholder="在此输入要编码或解码的文本..."
											className="flex-1 min-h-0 font-mono text-sm"
										/>
										<div className="flex justify-end space-x-2 mt-4">
											<Button variant="outline" onClick={decodeBase64}>解码</Button>
											<Button onClick={encodeBase64}>编码</Button>
										</div>
									</CardContent>
								</Card>

								<Card className="flex flex-col h-full">
									<CardContent className="flex flex-col h-full p-4">
										<div className="flex justify-between items-center mb-2">
											<h3 className="text-base font-medium">结果</h3>
											<Button variant="outline" size="sm" onClick={copyToClipboard} disabled={!textOutput}>
												<Copy className="w-4 h-4 mr-1" />
												复制
											</Button>
										</div>
										<pre className="flex-1 p-4 bg-gray-50 rounded border border-gray-200 overflow-auto font-mono text-sm">
											{textOutput || '编码/解码结果将显示在这里...'}
										</pre>
									</CardContent>
								</Card>
							</div>
						</TabsContent>

						<TabsContent value="image" className="flex-1 overflow-hidden">
							<div className="grid grid-cols-1 md:grid-cols-2 gap-6 h-full">
								<Card className="flex flex-col h-full">
									<CardContent className="flex flex-col h-full p-4">
										<div className="flex justify-between items-center mb-2">
											<h3 className="text-base font-medium flex items-center">
												<FileImage className="w-4 h-4 mr-2" />
												Base64 图片
											</h3>
											<div>
												<input
													type="file"
													accept="image/*"
													onChange={handleImageUpload}
													className="hidden"
													id="image-file-input"
												/>
												<label htmlFor="image-file-input">
													<Button variant="outline" size="sm" className="cursor-pointer" asChild>
														<span>
															<Upload className="w-4 h-4 mr-1" />
															上传图片
														</span>
													</Button>
												</label>
											</div>
										</div>
										<Textarea
											value={textInput}
											onChange={(e) => setTextInput(e.target.value)}
											placeholder="在此粘贴 Base64 图片字符串..."
											className="flex-1 min-h-0 font-mono text-sm"
										/>
										<div className="flex justify-end space-x-2 mt-4">
											<Button onClick={handleImageDecode}>解码图片</Button>
										</div>
									</CardContent>
								</Card>

								<Card className="flex flex-col h-full">
									<CardContent className="flex flex-col h-full p-4">
										<h3 className="text-base font-medium mb-2">图片预览</h3>
										<div className="flex-1 flex items-center justify-center bg-gray-50 rounded border border-gray-200 overflow-auto p-4">
											{imagePreview ? (
												<img
													src={imagePreview}
													alt="Base64 图片预览"
													className="max-w-full max-h-full object-contain"
													onError={() => {
														toast.error('无法显示图片，Base64 字符串可能无效')
														setImagePreview(null)
													}}
												/>
											) : (
												<div className="text-gray-400">图片预览将显示在这里...</div>
											)}
										</div>
									</CardContent>
								</Card>
							</div>
						</TabsContent>
					</Tabs>
				</div>
			</div>
		</div>
	)
}
