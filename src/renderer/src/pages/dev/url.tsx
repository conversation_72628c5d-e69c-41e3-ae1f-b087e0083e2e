import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent } from '@/components/ui/card'
import { Copy, Code } from 'lucide-react'
import { toast } from 'sonner'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

export const Route = createFileRoute('/dev/url')({
  component: UrlEncoder
})

function UrlEncoder() {
  const [input, setInput] = useState('')
  const [output, setOutput] = useState('')
  const [activeTab, setActiveTab] = useState('encode')

  // URL 参数解析
  const [parsedParams, setParsedParams] = useState<Record<string, string>>({})
  const [urlWithoutParams, setUrlWithoutParams] = useState('')

  const encodeUrl = () => {
    try {
      if (!input.trim()) {
        setOutput('')
        toast.error('请输入要编码的 URL')
        return
      }

      const encoded = encodeURIComponent(input)
      setOutput(encoded)
      toast.success('URL 编码成功')
    } catch (err) {
      toast.error(`编码错误: ${err instanceof Error ? err.message : String(err)}`)
    }
  }

  const decodeUrl = () => {
    try {
      if (!input.trim()) {
        setOutput('')
        toast.error('请输入要解码的 URL')
        return
      }

      const decoded = decodeURIComponent(input)
      setOutput(decoded)
      toast.success('URL 解码成功')
    } catch (err) {
      toast.error(`解码错误: ${err instanceof Error ? err.message : String(err)}`)
    }
  }

  const parseUrlParams = () => {
    try {
      if (!input.trim()) {
        setParsedParams({})
        setUrlWithoutParams('')
        toast.error('请输入要解析的 URL')
        return
      }

      const url = new URL(input.startsWith('http') ? input : `http://${input}`)
      setUrlWithoutParams(`${url.protocol}//${url.host}${url.pathname}`)

      const params: Record<string, string> = {}
      url.searchParams.forEach((value, key) => {
        params[key] = value
      })

      setParsedParams(params)
      toast.success('URL 参数解析成功')
    } catch (err) {
      toast.error(`URL 解析错误: ${err instanceof Error ? err.message : String(err)}`)
      setParsedParams({})
      setUrlWithoutParams('')
    }
  }

  const copyToClipboard = () => {
    if (!output) {
      toast.error('没有可复制的内容')
      return
    }

    navigator.clipboard.writeText(output)
      .then(() => toast.success('已复制到剪贴板'))
      .catch(() => toast.error('复制失败'))
  }

  return (
    <div className="flex flex-col min-h-0 h-full">
      <div className="flex-none">
        <h2 className="text-[#262626] text-xl font-medium">URL 编解码</h2>
        <p className="text-[#8C8C8C] text-sm mt-1">对 URL 进行编码、解码和参数解析</p>
      </div>

      <div className="flex-1 mt-6 min-h-0">
        <div className="h-full bg-white rounded-lg p-6">
          <Tabs defaultValue="encode" className="h-full flex flex-col" onValueChange={setActiveTab}>
            <TabsList className="mb-4 flex-none">
              <TabsTrigger value="encode">URL 编码</TabsTrigger>
              <TabsTrigger value="decode">URL 解码</TabsTrigger>
              <TabsTrigger value="parse">URL 参数解析</TabsTrigger>
            </TabsList>

            <TabsContent value="encode" className="flex-1 overflow-hidden">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 h-full">
                <Card className="flex flex-col h-full">
                  <CardContent className="flex flex-col h-full p-4">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="text-base font-medium flex items-center">
                        <Code className="w-4 h-4 mr-2" />
                        输入 URL
                      </h3>
                    </div>
                    <Textarea
                      value={input}
                      onChange={(e) => setInput(e.target.value)}
                      placeholder="在此输入要编码的 URL..."
                      className="flex-1 min-h-0 font-mono text-sm"
                    />
                    <div className="flex justify-end space-x-2 mt-4">
                      <Button onClick={encodeUrl}>编码</Button>
                    </div>
                  </CardContent>
                </Card>

                <Card className="flex flex-col h-full">
                  <CardContent className="flex flex-col h-full p-4">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="text-base font-medium">编码结果</h3>
                      <Button variant="outline" size="sm" onClick={copyToClipboard} disabled={!output}>
                        <Copy className="w-4 h-4 mr-1" />
                        复制
                      </Button>
                    </div>
                    <pre className="flex-1 p-4 bg-gray-50 rounded border border-gray-200 overflow-auto font-mono text-sm">
                      {output || '编码结果将显示在这里...'}
                    </pre>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="decode" className="flex-1 overflow-hidden">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 h-full">
                <Card className="flex flex-col h-full">
                  <CardContent className="flex flex-col h-full p-4">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="text-base font-medium flex items-center">
                        <Code className="w-4 h-4 mr-2" />
                        输入编码后的 URL
                      </h3>
                    </div>
                    <Textarea
                      value={input}
                      onChange={(e) => setInput(e.target.value)}
                      placeholder="在此输入要解码的 URL..."
                      className="flex-1 min-h-0 font-mono text-sm"
                    />
                    <div className="flex justify-end space-x-2 mt-4">
                      <Button onClick={decodeUrl}>解码</Button>
                    </div>
                  </CardContent>
                </Card>

                <Card className="flex flex-col h-full">
                  <CardContent className="flex flex-col h-full p-4">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="text-base font-medium">解码结果</h3>
                      <Button variant="outline" size="sm" onClick={copyToClipboard} disabled={!output}>
                        <Copy className="w-4 h-4 mr-1" />
                        复制
                      </Button>
                    </div>
                    <pre className="flex-1 p-4 bg-gray-50 rounded border border-gray-200 overflow-auto font-mono text-sm">
                      {output || '解码结果将显示在这里...'}
                    </pre>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="parse" className="flex-1 overflow-hidden">
              <div className="grid grid-cols-1 gap-6 h-full">
                <Card className="flex flex-col">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="text-base font-medium flex items-center">
                        <Code className="w-4 h-4 mr-2" />
                        输入完整 URL
                      </h3>
                    </div>
                    <div className="flex space-x-2">
                      <Input
                        value={input}
                        onChange={(e) => setInput(e.target.value)}
                        placeholder="https://example.com/path?param1=value1&param2=value2"
                        className="flex-1 font-mono text-sm"
                      />
                      <Button onClick={parseUrlParams}>解析</Button>
                    </div>
                  </CardContent>
                </Card>

                <Card className="flex flex-col flex-1 min-h-0">
                  <CardContent className="flex flex-col h-full p-4">
                    <h3 className="text-base font-medium mb-4">解析结果</h3>

                    {urlWithoutParams && (
                      <div className="mb-4">
                        <Label className="text-sm text-gray-500 mb-1">基础 URL</Label>
                        <pre className="p-2 bg-gray-50 rounded border border-gray-200 font-mono text-sm">
                          {urlWithoutParams}
                        </pre>
                      </div>
                    )}

                    <Label className="text-sm text-gray-500 mb-1">URL 参数</Label>
                    <div className="flex-1 min-h-0 overflow-auto">
                      {Object.keys(parsedParams).length > 0 ? (
                        <div className="space-y-2">
                          {Object.entries(parsedParams).map(([key, value], index) => (
                            <div key={index} className="grid grid-cols-3 gap-2 p-2 bg-gray-50 rounded border border-gray-200">
                              <div className="font-medium text-gray-700 font-mono">{key}</div>
                              <div className="col-span-2 font-mono break-all">{value}</div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="p-4 text-center text-gray-400">
                          {activeTab === 'parse' && input ? '没有找到 URL 参数' : '解析结果将显示在这里...'}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
