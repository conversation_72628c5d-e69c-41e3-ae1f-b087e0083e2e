import { createFileRoute } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { Copy, TerminalSquare, Upload, Info } from 'lucide-react'
import { toast } from 'sonner'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

export const Route = createFileRoute('/dev/regex')({
  component: RegexTester
})

interface RegexMatch {
  fullMatch: string
  groups: string[]
  index: number
}

interface RegexFlag {
  flag: string
  label: string
  description: string
}

function RegexTester() {
  const [pattern, setPattern] = useState('')
  const [testString, setTestString] = useState('')
  const [flags, setFlags] = useState<string[]>(['g'])
  const [matches, setMatches] = useState<RegexMatch[]>([])
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState('test')
  const [replacementPattern, setReplacementPattern] = useState('')
  const [replacementResult, setReplacementResult] = useState('')

  const regexFlags: RegexFlag[] = [
    { flag: 'g', label: '全局匹配', description: '查找所有匹配项，而不是在第一个匹配后停止' },
    { flag: 'i', label: '忽略大小写', description: '匹配时忽略大小写' },
    { flag: 'm', label: '多行匹配', description: '使 ^ 和 $ 匹配每行的开始和结束' },
    { flag: 's', label: '点号匹配换行', description: '允许 . 匹配换行符' },
    { flag: 'u', label: 'Unicode', description: '将模式视为 Unicode 序列' },
    { flag: 'y', label: '粘性匹配', description: '从目标字符串的当前位置开始匹配' }
  ]
  console.log("🚀 ~ RegexTester ~ regexFlags:", regexFlags)

  useEffect(() => {
    testRegex()
  }, [pattern, testString, flags])

  const toggleFlag = (flag: string) => {
    setFlags(prev =>
      prev.includes(flag)
        ? prev.filter(f => f !== flag)
        : [...prev, flag]
    )
  }

  const testRegex = () => {
    setMatches([])
    setError('')

    if (!pattern) return

    try {
      const regex = new RegExp(pattern, flags.join(''))

      if (!testString) return

      const results: RegexMatch[] = []
      let match

      if (flags.includes('g')) {
        while ((match = regex.exec(testString)) !== null) {
          results.push({
            fullMatch: match[0],
            groups: match.slice(1),
            index: match.index
          })

          // 防止零宽度匹配导致的无限循环
          if (match[0].length === 0) {
            regex.lastIndex++
          }
        }
      } else {
        match = regex.exec(testString)
        if (match) {
          results.push({
            fullMatch: match[0],
            groups: match.slice(1),
            index: match.index
          })
        }
      }

      setMatches(results)
    } catch (err) {
      setError(`正则表达式错误: ${err instanceof Error ? err.message : String(err)}`)
    }
  }

  const replaceText = () => {
    if (!pattern) {
      toast.error('请输入正则表达式')
      return
    }

    try {
      const regex = new RegExp(pattern, flags.join(''))
      const result = testString.replace(regex, replacementPattern)
      setReplacementResult(result)
      toast.success('替换成功')
    } catch (err) {
      toast.error(`替换错误: ${err instanceof Error ? err.message : String(err)}`)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
      .then(() => toast.success('已复制到剪贴板'))
      .catch(() => toast.error('复制失败'))
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      setTestString(content)
    }
    reader.onerror = () => {
      toast.error('文件读取失败')
    }
    reader.readAsText(file)
  }

  const highlightMatches = () => {
    if (!testString || matches.length === 0) return testString

    let result = testString
    let offset = 0

    // 按索引排序匹配项
    const sortedMatches = [...matches].sort((a, b) => a.index - b.index)

    for (const match of sortedMatches) {
      const startIndex = match.index + offset
      const endIndex = startIndex + match.fullMatch.length

      const before = result.substring(0, startIndex)
      const matchText = result.substring(startIndex, endIndex)
      const after = result.substring(endIndex)

      result = `${before}<mark class="bg-yellow-200">${matchText}</mark>${after}`

      // 更新偏移量，考虑添加的 HTML 标签长度
      offset += '<mark class="bg-yellow-200">'.length + '</mark>'.length
    }

    return result
  }

  return (
    <div className="flex flex-col min-h-0 h-full">
      <div className="flex-none">
        <h2 className="text-[#262626] text-xl font-medium">正则表达式测试</h2>
        <p className="text-[#8C8C8C] text-sm mt-1">测试和验证正则表达式，查看匹配结果</p>
      </div>

      <div className="flex-1 mt-6 min-h-0">
        <div className="h-full bg-white rounded-lg p-6">
          <Tabs defaultValue="test" className="h-full flex flex-col" onValueChange={setActiveTab}>
            <TabsList className="mb-4 flex-none">
              <TabsTrigger value="test">正则测试</TabsTrigger>
              <TabsTrigger value="replace">正则替换</TabsTrigger>
            </TabsList>

            <TabsContent value="test" className="flex-1 overflow-hidden">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 h-full">
                <div className="flex flex-col space-y-6">
                  <Card className="flex-none">
                    <CardContent className="p-4">
                      <div className="space-y-4">
                        <div>
                          <div className="flex justify-between items-center mb-2">
                            <Label htmlFor="regex-pattern" className="font-medium">正则表达式</Label>
                            <div className="text-sm text-gray-500">
                              {error && <span className="text-red-500">{error}</span>}
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <div className="flex-none">/</div>
                            <Input
                              id="regex-pattern"
                              value={pattern}
                              onChange={(e) => setPattern(e.target.value)}
                              placeholder="输入正则表达式..."
                              className="flex-1 font-mono"
                            />
                            <div className="flex-none">/</div>
                            <Input
                              value={flags.join('')}
                              readOnly
                              className="w-16 font-mono text-center"
                            />
                          </div>
                        </div>

                        <div>
                          <Label className="font-medium mb-2 block">正则标志</Label>
                          <div className="grid grid-cols-3 gap-2">
                            {regexFlags.map((flag) => (
                              <div key={flag.flag} className="flex items-center space-x-2">
                                <Checkbox
                                  id={`flag-${flag.flag}`}
                                  checked={flags.includes(flag.flag)}
                                  onCheckedChange={() => toggleFlag(flag.flag)}
                                />
                                <div className="grid gap-1">
                                  <Label
                                    htmlFor={`flag-${flag.flag}`}
                                    className="text-sm font-normal"
                                  >
                                    {flag.label} ({flag.flag})
                                  </Label>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="flex-1 flex flex-col">
                    <CardContent className="p-4 flex flex-col h-full">
                      <div className="flex justify-between items-center mb-2">
                        <Label htmlFor="test-string" className="font-medium">测试文本</Label>
                        <div>
                          <input
                            type="file"
                            accept=".txt"
                            onChange={handleFileUpload}
                            className="hidden"
                            id="text-file-input"
                          />
                          <label htmlFor="text-file-input">
                            <Button variant="outline" size="sm" className="cursor-pointer" asChild>
                              <span>
                                <Upload className="w-4 h-4 mr-1" />
                                上传文件
                              </span>
                            </Button>
                          </label>
                        </div>
                      </div>
                      <Textarea
                        id="test-string"
                        value={testString}
                        onChange={(e) => setTestString(e.target.value)}
                        placeholder="输入要测试的文本..."
                        className="flex-1 min-h-0 font-mono"
                      />
                    </CardContent>
                  </Card>
                </div>

                <Card className="flex flex-col h-full">
                  <CardContent className="p-4 flex flex-col h-full">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-base font-medium">匹配结果</h3>
                      <div className="text-sm text-gray-500">
                        找到 {matches.length} 个匹配
                      </div>
                    </div>

                    <div className="flex-1 min-h-0 overflow-auto">
                      {matches.length > 0 ? (
                        <div className="space-y-6">
                          <div>
                            <h4 className="text-sm font-medium mb-2">高亮显示</h4>
                            <div
                              className="p-4 bg-gray-50 rounded border border-gray-200 overflow-auto whitespace-pre-wrap"
                              dangerouslySetInnerHTML={{ __html: highlightMatches() }}
                            />
                          </div>

                          <div>
                            <h4 className="text-sm font-medium mb-2">匹配详情</h4>
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead className="w-16">序号</TableHead>
                                  <TableHead>匹配内容</TableHead>
                                  <TableHead className="w-16">位置</TableHead>
                                  <TableHead className="w-24">操作</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {matches.map((match, index) => (
                                  <TableRow key={index}>
                                    <TableCell className="font-medium">{index + 1}</TableCell>
                                    <TableCell className="font-mono">
                                      {match.fullMatch}
                                      {match.groups.length > 0 && (
                                        <TooltipProvider>
                                          <Tooltip>
                                            <TooltipTrigger asChild>
                                              <Button variant="ghost" size="icon" className="h-6 w-6 ml-1">
                                                <Info className="h-3 w-3" />
                                              </Button>
                                            </TooltipTrigger>
                                            <TooltipContent>
                                              <div className="text-xs">
                                                <div className="font-medium mb-1">捕获组:</div>
                                                {match.groups.map((group, i) => (
                                                  <div key={i} className="font-mono">
                                                    ${i + 1}: {group || '<空>'}
                                                  </div>
                                                ))}
                                              </div>
                                            </TooltipContent>
                                          </Tooltip>
                                        </TooltipProvider>
                                      )}
                                    </TableCell>
                                    <TableCell>{match.index}</TableCell>
                                    <TableCell>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => copyToClipboard(match.fullMatch)}
                                      >
                                        <Copy className="h-3 w-3 mr-1" />
                                        复制
                                      </Button>
                                    </TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </div>
                        </div>
                      ) : (
                        <div className="flex items-center justify-center h-full text-gray-400">
                          {pattern ? '没有找到匹配项' : '请输入正则表达式'}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="replace" className="flex-1 overflow-hidden">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 h-full">
                <div className="flex flex-col space-y-6">
                  <Card className="flex-none">
                    <CardContent className="p-4">
                      <div className="space-y-4">
                        <div>
                          <div className="flex justify-between items-center mb-2">
                            <Label htmlFor="regex-pattern-replace" className="font-medium">正则表达式</Label>
                            <div className="text-sm text-gray-500">
                              {error && <span className="text-red-500">{error}</span>}
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <div className="flex-none">/</div>
                            <Input
                              id="regex-pattern-replace"
                              value={pattern}
                              onChange={(e) => setPattern(e.target.value)}
                              placeholder="输入正则表达式..."
                              className="flex-1 font-mono"
                            />
                            <div className="flex-none">/</div>
                            <Input
                              value={flags.join('')}
                              readOnly
                              className="w-16 font-mono text-center"
                            />
                          </div>
                        </div>

                        <div>
                          <Label htmlFor="replacement-pattern" className="font-medium mb-2 block">替换文本</Label>
                          <Input
                            id="replacement-pattern"
                            value={replacementPattern}
                            onChange={(e) => setReplacementPattern(e.target.value)}
                            placeholder="输入替换文本，可使用 $1, $2 等引用捕获组..."
                            className="font-mono"
                          />
                        </div>

                        <div className="flex justify-end">
                          <Button onClick={replaceText}>执行替换</Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="flex-1 flex flex-col">
                    <CardContent className="p-4 flex flex-col h-full">
                      <div className="flex justify-between items-center mb-2">
                        <Label htmlFor="test-string-replace" className="font-medium">原始文本</Label>
                        <div>
                          <input
                            type="file"
                            accept=".txt"
                            onChange={handleFileUpload}
                            className="hidden"
                            id="text-file-input-replace"
                          />
                          <label htmlFor="text-file-input-replace">
                            <Button variant="outline" size="sm" className="cursor-pointer" asChild>
                              <span>
                                <Upload className="w-4 h-4 mr-1" />
                                上传文件
                              </span>
                            </Button>
                          </label>
                        </div>
                      </div>
                      <Textarea
                        id="test-string-replace"
                        value={testString}
                        onChange={(e) => setTestString(e.target.value)}
                        placeholder="输入要替换的文本..."
                        className="flex-1 min-h-0 font-mono"
                      />
                    </CardContent>
                  </Card>
                </div>

                <Card className="flex flex-col h-full">
                  <CardContent className="p-4 flex flex-col h-full">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="text-base font-medium">替换结果</h3>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(replacementResult)}
                        disabled={!replacementResult}
                      >
                        <Copy className="h-4 w-4 mr-1" />
                        复制结果
                      </Button>
                    </div>

                    <div className="flex-1 p-4 bg-gray-50 rounded border border-gray-200 overflow-auto whitespace-pre-wrap font-mono">
                      {replacementResult || '替换结果将显示在这里...'}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
