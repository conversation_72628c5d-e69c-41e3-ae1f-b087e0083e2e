import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent } from '@/components/ui/card'
import { Copy, FileJson, Upload } from 'lucide-react'
import { toast } from 'sonner'

export const Route = createFileRoute('/dev/json')({
  component: JsonFormatter
})

function JsonFormatter() {
  const [input, setInput] = useState('')
  const [output, setOutput] = useState('')
  const [error, setError] = useState('')

  const formatJson = () => {
    try {
      if (!input.trim()) {
        setOutput('')
        setError('请输入 JSON 字符串')
        return
      }

      // 解析并格式化 JSON
      const parsedJson = JSON.parse(input)
      const formattedJson = JSON.stringify(parsedJson, null, 2)
      setOutput(formattedJson)
      setError('')
      toast.success('JSON 格式化成功')
    } catch (err) {
      setError(`JSON 解析错误: ${err instanceof Error ? err.message : String(err)}`)
      setOutput('')
    }
  }

  const compactJson = () => {
    try {
      if (!input.trim()) {
        setOutput('')
        setError('请输入 JSON 字符串')
        return
      }

      // 解析并压缩 JSON
      const parsedJson = JSON.parse(input)
      const compactedJson = JSON.stringify(parsedJson)
      setOutput(compactedJson)
      setError('')
      toast.success('JSON 压缩成功')
    } catch (err) {
      setError(`JSON 解析错误: ${err instanceof Error ? err.message : String(err)}`)
      setOutput('')
    }
  }

  const copyToClipboard = () => {
    if (!output) {
      toast.error('没有可复制的内容')
      return
    }

    navigator.clipboard.writeText(output)
      .then(() => toast.success('已复制到剪贴板'))
      .catch(() => toast.error('复制失败'))
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      setInput(content)
    }
    reader.onerror = () => {
      toast.error('文件读取失败')
    }
    reader.readAsText(file)
  }

  return (
    <div className="flex flex-col min-h-0 h-full">
      <div className="flex-none">
        <h2 className="text-[#262626] text-xl font-medium">JSON 格式化</h2>
        <p className="text-[#8C8C8C] text-sm mt-1">格式化或压缩 JSON 数据，便于阅读和分析</p>
      </div>

      <div className="flex-1 mt-6 min-h-0">
        <div className="h-full bg-white rounded-lg p-6">
          <div className="h-full grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="flex flex-col h-full">
              <CardContent className="flex flex-col h-full p-4">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-base font-medium flex items-center">
                    <FileJson className="w-4 h-4 mr-2" />
                    输入 JSON
                  </h3>
                  <div>
                    <input
                      type="file"
                      accept=".json"
                      onChange={handleFileUpload}
                      className="hidden"
                      id="json-file-input"
                    />
                    <label htmlFor="json-file-input">
                      <Button variant="outline" size="sm" className="cursor-pointer" asChild>
                        <span>
                          <Upload className="w-4 h-4 mr-1" />
                          上传文件
                        </span>
                      </Button>
                    </label>
                  </div>
                </div>
                <Textarea
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder="在此粘贴 JSON 数据..."
                  className="flex-1 min-h-0 font-mono text-sm"
                />
                <div className="flex justify-end space-x-2 mt-4">
                  <Button variant="outline" onClick={compactJson}>压缩</Button>
                  <Button onClick={formatJson}>格式化</Button>
                </div>
              </CardContent>
            </Card>

            <Card className="flex flex-col h-full">
              <CardContent className="flex flex-col h-full p-4">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-base font-medium">结果</h3>
                  <Button variant="outline" size="sm" onClick={copyToClipboard} disabled={!output}>
                    <Copy className="w-4 h-4 mr-1" />
                    复制
                  </Button>
                </div>
                {error ? (
                  <div className="flex-1 p-4 bg-red-50 text-red-500 rounded border border-red-200 overflow-auto">
                    {error}
                  </div>
                ) : (
                  <pre className="flex-1 p-4 bg-gray-50 rounded border border-gray-200 overflow-auto font-mono text-sm">
                    {output || '格式化后的 JSON 将显示在这里...'}
                  </pre>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
