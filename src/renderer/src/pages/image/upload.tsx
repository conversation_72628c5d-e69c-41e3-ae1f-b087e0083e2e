import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { ImageUploadForm } from '@renderer/components/image/ImageUploadForm'
import { getFormatIcon } from '@renderer/utils/fileIcons'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Copy } from 'lucide-react'
import { FileUploadList } from '@renderer/components/common/FileUploadList'
import { useImageStore } from '@renderer/store/imageStore'

export const Route = createFileRoute('/image/upload')({
	component: ImageUpload
})

function ImageUpload() {
	// 使用 Zustand store 管理文件状态
	const {
		files: storeFiles,
		isProcessing,
		setProcessing
	} = useImageStore()

	// 将 store 中的文件对象转换为路径数组，以兼容现有组件
	const files = storeFiles.map(file => file.path)

	const [uploadSettings, setUploadSettings] = useState({
		platform: 'github',
		token: '',
		repository: '',
		path: 'images/'
	})
	const [progress, setProgress] = useState(0)
	const [processedCount, setProcessedCount] = useState(0)
	const [uploadResults, setUploadResults] = useState<{ file: string, url: string }[]>([])



	const handleUpload = () => {
		setProcessing(true)
		setProgress(0)
		setProcessedCount(0)

		// 模拟进度更新
		const totalFiles = files.length
		let processed = 0

		const interval = setInterval(() => {
			if (processed < totalFiles) {
				processed++
				setProcessedCount(processed)
				setProgress((processed / totalFiles) * 100)

				if (processed === totalFiles) {
					// 模拟上传结果
					const newResults = files.map(file => ({
						file,
						url: `https://example.com/images/${file.split('/').pop()}`
					}))
					setUploadResults(newResults)
					setProcessing(false)
					clearInterval(interval)
				}
			}
		}, 500)
	}

	const handleStop = () => {
		setProcessing(false)
		setProgress(0)
		setProcessedCount(0)
	}

	const handleSettingsChange = (settings: Partial<typeof uploadSettings>) => {
		setUploadSettings({ ...uploadSettings, ...settings })
	}

	const handleCopyUrl = (url: string) => {
		navigator.clipboard.writeText(url)
			.then(() => {
				console.log('URL copied to clipboard')
			})
			.catch(err => {
				console.error('Failed to copy URL: ', err)
			})
	}

	return (
		<div className="flex flex-col min-h-0 h-full">
			{/* 页面标题 */}
			<div className="flex-none">
				<h2 className="text-[#262626] text-xl font-medium">图床上传</h2>
				<p className="text-[#8C8C8C] text-sm mt-1">将图片上传至图床，获取在线链接</p>
			</div>

			<div className="flex-1 mt-6 min-h-0">
				<div className="grid grid-cols-1 md:grid-cols-3 gap-0 rounded-lg overflow-hidden bg-white h-full">

					{/* 文件列表区 */}
					<div className="md:col-span-2 border-r border-slate-200 overflow-auto">
						<FileUploadList
							storeType="image"
						/>
					</div>

					{/* 上传设置区 */}
					<div className="md:col-span-1 overflow-auto">
						<div className="space-y-6 p-1">
							<Card>
								<CardHeader>
									<CardTitle className="text-base font-medium">上传设置</CardTitle>
								</CardHeader>
								<CardContent>
									<ImageUploadForm
										settings={uploadSettings}
										onSettingsChange={handleSettingsChange}
										onUpload={handleUpload}
										onStop={handleStop}
										disabled={files.length === 0 || !uploadSettings.token || isProcessing}
										isProcessing={isProcessing}
										progress={progress}
										processedCount={processedCount}
										totalCount={files.length}
									/>
								</CardContent>
							</Card>

							{/* 上传结果区 */}
							{uploadResults.length > 0 && (
								<Card>
									<CardHeader>
										<CardTitle className="text-base font-medium">上传结果</CardTitle>
									</CardHeader>
									<CardContent>
										<div className="space-y-2">
											{uploadResults.map((result, index) => (
												<div key={index} className="flex items-center justify-between p-3 border border-[#D9D9D9] rounded-md">
													<div className="flex items-center truncate flex-1">
														<span className="mr-2">{getFormatIcon(result.file)}</span>
														<div className="truncate">{result.url}</div>
													</div>
													<Button
														variant="outline"
														size="sm"
														onClick={() => handleCopyUrl(result.url)}
														className="ml-2"
													>
														<Copy className="h-4 w-4 mr-1" />
														复制
													</Button>
												</div>
											))}
										</div>
									</CardContent>
								</Card>
							)}
						</div>
					</div>
				</div>
			</div>
		</div>
	)
}
