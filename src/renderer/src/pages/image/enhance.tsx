import { createFileRoute } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { ImageEnhanceForm } from '@renderer/components/image/ImageEnhanceForm'
import { FileUploadList } from '@renderer/components/common/FileUploadList'
import { useImageStore } from '@renderer/store/imageStore'
import { toast } from 'sonner'
import { MainProcessNoticeType } from '@renderer/types'
import path from 'path-browserify-esm'

export const Route = createFileRoute('/image/enhance')({
  component: ImageEnhance
})

function ImageEnhance() {
  // 使用 Zustand store 管理文件状态
  const {
    files: storeFiles,
    currentFile,
    isProcessing,
    setCurrentFile,
    setProcessing,
    setFileStatus,
    resetFileStatus
  } = useImageStore()

  // 将 store 中的文件对象转换为路径数组
  const files = storeFiles.map(file => file.path)

  // 设置状态
  const [enhanceSettings, setEnhanceSettings] = useState({
    scale: 2, // 默认放大2倍
    sharpness: 50, // 默认锐化程度50%
    denoise: true, // 默认启用降噪
    denoiseLevel: 30, // 默认降噪级别30%
    saveDirectory: '',
    deleteOriginal: false
  })

  const [processedCount, setProcessedCount] = useState(0)
  const [totalCount, setTotalCount] = useState(0)
  const [overallProgress, setOverallProgress] = useState(0)

  // 监听主进程通知
  useEffect(() => {
    const unsubscribe = window.api.mainProcessNotice((type, data, filePath) => {
      switch (type) {
        case MainProcessNoticeType.START:
          console.log('开始处理:', data, filePath)
          setFileStatus(filePath, 0) // 设置文件状态为0%
          break
        case MainProcessNoticeType.PROGRESS:
          console.log('处理进度:', data, filePath)
          setFileStatus(filePath, data) // 设置文件进度
          break
        case MainProcessNoticeType.SUCCESS:
          console.log('处理成功:', data, filePath)
          setFileStatus(filePath, 100) // 设置文件状态为100%
          setProcessedCount(prev => prev + 1)
          toast.success('图片清晰度提升成功', { description: path.basename(filePath) })
          break
        case MainProcessNoticeType.ERROR:
          console.error('处理错误:', data, filePath)
          setFileStatus(filePath, -1) // 设置文件状态为错误
          toast.error('图片清晰度提升失败', { description: data })
          break
        case MainProcessNoticeType.STOP:
          console.log('处理停止:', data, filePath)
          setProcessing(false)
          toast.info('处理已停止')
          break
      }
    })

    return () => {
      unsubscribe()
    }
  }, [setFileStatus, setProcessing])

  // 当文件列表变化且不在处理状态时，更新计数
  useEffect(() => {
    if (!isProcessing) {
      setTotalCount(files.length)
      setProcessedCount(0)
    }
  }, [files.length, isProcessing])

  // 选择保存目录
  const handleSelectDirectory = async () => {
    try {
      const directory = await window.api.selectDirectory()
      if (directory) {
        setEnhanceSettings(prev => ({ ...prev, saveDirectory: directory }))
      }
    } catch (error) {
      console.error('Error selecting directory:', error)
      toast.error('选择目录失败', { description: String(error) })
    }
  }

  // 处理图片清晰度提升
  const handleEnhance = async () => {
    if (files.length === 0) {
      toast.error('请先选择图片文件')
      return
    }

    try {
      // 重置处理状态
      resetFileStatus()
      setProcessedCount(0)
      setTotalCount(files.length)
      setProcessing(true)

      // 创建一个局部变量来跟踪处理状态
      let shouldContinue = true;

      // 添加一个事件监听器来检测停止信号
      const stopListener = window.api.mainProcessNotice((type) => {
        if (type === MainProcessNoticeType.STOP) {
          shouldContinue = false;
        }
      });

      try {
        // 依次处理每个文件
        for (let i = 0; i < files.length; i++) {
          // 如果处理被停止，跳出循环
          if (!shouldContinue) {
            break;
          }

          const filePath = files[i];
          await enhanceFile(filePath);
        }

        // 只有在正常完成时才显示成功消息
        if (shouldContinue) {
          toast.success('所有图片处理完成');
        }
      } finally {
        // 确保移除事件监听器
        stopListener();
      }

      setProcessing(false)
    } catch (error) {
      console.error('Error enhancing images:', error)
      toast.error('图片处理失败', { description: String(error) })
      setProcessing(false)
    }
  }

  // 停止处理
  const handleStop = () => {
    window.api.stop()
    setProcessing(false)
  }

  // 处理单个文件
  const enhanceFile = async (filePath: string) => {
    try {
      // 构建输出路径
      let outputPath = ''
      if (enhanceSettings.saveDirectory) {
        // 从文件路径中提取文件名和扩展名
        const fileName = path.basename(filePath)
        const ext = path.extname(filePath)
        // 构建新的输出路径
        outputPath = `${enhanceSettings.saveDirectory}/${fileName.replace(/\.[^/.]+$/, '')}-enhanced${ext}`
      }

      // 调用主进程的图片清晰度提升函数
      await window.api.enhanceImage({
        inputPath: filePath,
        outputPath,
        scale: enhanceSettings.scale,
        sharpness: enhanceSettings.sharpness,
        denoise: enhanceSettings.denoise,
        denoiseLevel: enhanceSettings.denoiseLevel,
        deleteOriginal: enhanceSettings.deleteOriginal
      })
    } catch (error) {
      console.error('Error enhancing image:', error)
      toast.error('图片清晰度提升失败', { description: String(error) })
      setFileStatus(filePath, -1)
    }
  }

  // 计算总体进度
  useEffect(() => {
    if (!isProcessing || files.length === 0) {
      setOverallProgress(0)
      return
    }

    let totalProgress = 0
    let validFiles = 0

    for (const file of storeFiles) {
      if (typeof file.status === 'number' && file.status >= 0) {
        totalProgress += file.status
        validFiles++
      }
    }

    const avgProgress = validFiles > 0 ? totalProgress / validFiles : 0
    setOverallProgress(avgProgress)
  }, [storeFiles, isProcessing, files.length])

  return (
    <div className="flex flex-col min-h-0 h-full">
      {/* 页面标题 */}
      <div className="flex-none">
        <h2 className="text-[#262626] text-xl font-medium">图片清晰度提升</h2>
        <p className="text-[#8C8C8C] text-sm mt-1">提高图片分辨率和清晰度，让模糊图片变得更加清晰</p>
      </div>

      <div className="flex-1 mt-6 min-h-0">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-0 rounded-lg overflow-hidden bg-white h-full">
          {/* 文件列表区 */}
          <div className="md:col-span-2 border-r border-slate-200 overflow-auto">
            <FileUploadList
              storeType="image"
            />
          </div>

          {/* 设置区 */}
          <div className="p-6 overflow-auto">
            <ImageEnhanceForm
              settings={enhanceSettings}
              onSettingsChange={(newSettings) => setEnhanceSettings(prev => ({ ...prev, ...newSettings }))}
              onSelectDirectory={handleSelectDirectory}
              onEnhance={handleEnhance}
              onStop={handleStop}
              disabled={files.length === 0}
              isProcessing={isProcessing}
              progress={overallProgress}
              processedCount={processedCount}
              totalCount={totalCount}
            />
          </div>
        </div>
      </div>
    </div>
  )
}
