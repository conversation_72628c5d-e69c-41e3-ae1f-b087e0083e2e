import { createFileRoute } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { ImageCompressForm } from '@renderer/components/image/ImageCompressForm'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { FileUploadList } from '@renderer/components/common/FileUploadList'
import { useImageStore } from '@renderer/store/imageStore'
import { toast } from 'sonner'
import { MainProcessNoticeType } from '@renderer/types'

export const Route = createFileRoute('/image/compress')({
  component: ImageCompress
})

function ImageCompress() {
  // 使用 Zustand store 管理文件状态
  const {
    files: storeFiles,
    currentFile,
    isProcessing,
    setCurrentFile,
    setProcessing,
    setFileStatus
  } = useImageStore()

  // 将 store 中的文件对象转换为路径数组，以兼容现有组件
  const files = storeFiles.map(file => file.path)

  // 构建文件状态对象，用于传递给 FileList 组件
  const fileProgress: Record<string, number> = {}
  storeFiles.forEach(file => {
    if (file.status !== undefined && typeof file.status === 'number') {
      fileProgress[file.path] = file.status
    }
  })

  const [compressSettings, setCompressSettings] = useState({
    quality: 80,
    saveDirectory: '',
    deleteOriginal: false
  })
  const [progress, setProgress] = useState(0)
  const [processedCount, setProcessedCount] = useState(0)

  // 获取文件名的辅助函数
  const getFileName = (filePath: string) => {
    // 处理不同操作系统的路径分隔符
    const normalizedPath = filePath.replace(/\\/g, '/');
    return normalizedPath.split('/').pop() || filePath;
  };

  // 监听主进程通知
  useEffect(() => {
    const unsubscribe = window.api.mainProcessNotice((type, data, filePath) => {
      console.log("🚀 ~ unsubscribe ~ type, data, filePath:", type, data, filePath)

      switch (type) {
        case MainProcessNoticeType.START:
          setCurrentFile(filePath)
          setProgress(0)
          setFileStatus(filePath, 0)
          toast.info('开始压缩图片', { description: getFileName(filePath) })
          break
        case MainProcessNoticeType.PROGRESS:
          setProgress(data)
          setFileStatus(filePath, 40)
          break
        case MainProcessNoticeType.SUCCESS:
          console.log("🚀 ~ unsubscribe ~ MainProcessNoticeType.SUCCESS:", MainProcessNoticeType.SUCCESS)

          toast.success('图片压缩成功', { description: getFileName(filePath) })
          setFileStatus(filePath, 100)
          setProcessedCount(prev => prev + 1)

          // 检查是否所有文件都已处理完成
          const completedFiles = storeFiles.filter(f => f.status === 100 || f.status === -1).length
          if (completedFiles === files.length) {
            setProcessing(false)
            toast.success('所有图片压缩完成', { description: `共处理 ${completedFiles} 个文件` })
          }
          break
        case MainProcessNoticeType.ERROR:
          toast.error('图片压缩失败', { description: data })
          setFileStatus(filePath, -1) // -1 表示错误

          // 检查是否所有文件都已处理完成
          const processedFiles = storeFiles.filter(f => f.status === 100 || f.status === -1).length
          if (processedFiles === files.length) {
            setProcessing(false)
          }
          break
        case MainProcessNoticeType.STOP:
          toast.info('图片压缩已停止')
          setProcessing(false)
          setCurrentFile(null)
          setProgress(0)
          break
      }
    })

    return () => {
      unsubscribe()
    }
  }, [files, currentFile, storeFiles, setCurrentFile, setFileStatus, setProcessing])

  const compressFile = async (filePath: string) => {
    try {
      // 构建输出路径
      let outputPath = ''
      if (compressSettings.saveDirectory) {
        // 从文件路径中提取文件名和扩展名
        const fileName = getFileName(filePath)
        const ext = filePath.substring(filePath.lastIndexOf('.'))
        // 构建新的输出路径
        outputPath = `${compressSettings.saveDirectory}/${fileName.replace(/\.[^/.]+$/, '')}-compressed${ext}`
      }

      // 调用主进程的压缩函数
      await window.api.compressImage({
        inputPath: filePath,
        outputPath,
        quality: compressSettings.quality,
        deleteOriginal: compressSettings.deleteOriginal
      })
    } catch (error) {
      console.error('Error compressing image:', error)
      toast.error('图片压缩失败', { description: String(error) })
      setFileStatus(filePath, -1)
    }
  }

  const handleCompress = async () => {
    if (files.length === 0) {
      toast.error('请先选择图片文件')
      return
    }

    setProcessing(true)
    setProcessedCount(0)

    // 重置所有文件状态
    files.forEach(file => setFileStatus(file, 0))

    // 开始压缩所有文件
    files.forEach(file => compressFile(file))
  }

  const handleStop = () => {
    window.api.stop()
    setProcessing(false)
    setCurrentFile(null)
    setProgress(0)
  }

  const handleSettingsChange = (settings: Partial<typeof compressSettings>) => {
    setCompressSettings({ ...compressSettings, ...settings })
  }

  const handleSelectDirectory = async () => {
    try {
      const directory = await window.api.selectDirectory()
      if (directory) {
        setCompressSettings({ ...compressSettings, saveDirectory: directory })
      }
    } catch (error) {
      console.error('Error selecting directory:', error)
    }
  }

  return (
    <div className="flex flex-col min-h-0 h-full">
      {/* 页面标题 */}
      <div className="flex-none">
        <h2 className="text-[#262626] text-xl font-medium">图片压缩</h2>
        <p className="text-[#8C8C8C] text-sm mt-1">压缩图片文件大小，保持较好的视觉质量</p>
      </div>

      <div className="flex-1 mt-6 min-h-0">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-0 rounded-lg overflow-hidden bg-white h-full">
          {/* 文件列表区 */}
          <div className="md:col-span-2 border-r border-slate-200 overflow-auto">
            <FileUploadList
              storeType="image"
            />
          </div>

          {/* 压缩设置区 */}
          <div className="md:col-span-1 overflow-auto">
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="text-base font-medium">压缩设置</CardTitle>
              </CardHeader>
              <CardContent>
                <ImageCompressForm
                  settings={compressSettings}
                  onSettingsChange={handleSettingsChange}
                  onSelectDirectory={handleSelectDirectory}
                  onCompress={handleCompress}
                  onStop={handleStop}
                  disabled={files.length === 0 || isProcessing}
                  isProcessing={isProcessing}
                  progress={progress}
                  processedCount={processedCount}
                  totalCount={files.length}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
