import { createFileRoute } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { ImageConvertForm } from '@renderer/components/image/ImageConvertForm'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { FileUploadList } from '@renderer/components/common/FileUploadList'
import { useImageStore } from '@renderer/store/imageStore'
import { MainProcessNoticeType } from '@renderer/types'
import { toast } from 'sonner'

export const Route = createFileRoute('/image/convert')({
	component: ImageConvert
})

function ImageConvert() {
	// 使用 Zustand store 管理文件状态
	const {
		files: storeFiles,
		currentFile,
		isProcessing,
		setCurrentFile,
		setProcessing,
		setFileStatus
	} = useImageStore()

	// 将 store 中的文件对象转换为路径数组，以兼容现有组件
	const files = storeFiles.map(file => file.path)

	const [outputFormat, setOutputFormat] = useState('jpg')
	const [saveDirectory, setSaveDirectory] = useState('')
	const [progress, setProgress] = useState(0)
	const [deleteOriginal, setDeleteOriginal] = useState(false)
	const [processedCount, setProcessedCount] = useState(0)

	// 获取文件名的辅助函数
	const getFileName = (filePath: string) => {
		// 处理不同操作系统的路径分隔符
		const normalizedPath = filePath.replace(/\\/g, '/');
		return normalizedPath.split('/').pop() || filePath;
	};

	// 监听主进程通知
	useEffect(() => {
		const unsubscribe = window.api.mainProcessNotice((type, data, filePath) => {
			switch (type) {
				case MainProcessNoticeType.START:
					setCurrentFile(filePath)
					setProgress(0)
					setFileStatus(filePath, 0)
					toast.info('开始转换图片格式', { description: getFileName(filePath) })
					break
				case MainProcessNoticeType.PROGRESS:
					setProgress(data)
					setFileStatus(filePath, 40)
					break
				case MainProcessNoticeType.SUCCESS:
					toast.success('图片格式转换成功', { description: getFileName(filePath) })
					setFileStatus(filePath, 100)
					setProcessedCount(prev => prev + 1)

					// 检查是否所有文件都已处理完成
					const completedFiles = storeFiles.filter(f => f.status === 100 || f.status === -1).length
					if (completedFiles === files.length) {
						setProcessing(false)
						toast.success('所有图片格式转换完成', { description: `共处理 ${completedFiles} 个文件` })
					}
					break
				case MainProcessNoticeType.ERROR:
					toast.error('图片格式转换失败', { description: data })
					setFileStatus(filePath, -1) // -1 表示错误

					// 检查是否所有文件都已处理完成
					const processedFiles = storeFiles.filter(f => f.status === 100 || f.status === -1).length
					if (processedFiles === files.length) {
						setProcessing(false)
					}
					break
				case MainProcessNoticeType.STOP:
					toast.info('图片格式转换已停止')
					setProcessing(false)
					setCurrentFile(null)
					setProgress(0)
					break
			}
		})

		return () => {
			unsubscribe()
		}
	}, [files, currentFile, storeFiles, setCurrentFile, setFileStatus, setProcessing])

	const convertFile = async (filePath: string) => {
		try {
			// 构建输出路径
			let outputPath = ''
			if (saveDirectory) {
				// 从文件路径中提取文件名和扩展名
				const fileName = getFileName(filePath)
				// 构建新的输出路径
				outputPath = `${saveDirectory}/${fileName.replace(/\.[^/.]+$/, '')}.${outputFormat}`
			}

			// 调用主进程的格式转换函数
			await window.api.convertImageFormat({
				inputPath: filePath,
				outputPath,
				format: outputFormat,
				quality: 80, // 默认质量
				deleteOriginal
			})
		} catch (error) {
			console.error('Error converting image format:', error)
			toast.error('图片格式转换失败', { description: String(error) })
			setFileStatus(filePath, -1)
		}
	}

	const handleConvert = async () => {
		if (files.length === 0) {
			toast.error('请先选择图片文件')
			return
		}

		setProcessing(true)
		setProcessedCount(0)

		// 重置所有文件状态
		files.forEach(file => setFileStatus(file, 0))

		// 开始转换所有文件
		files.forEach(file => convertFile(file))
	}

	const handleStop = () => {
		window.api.stop()
		setProcessing(false)
		setCurrentFile(null)
		setProgress(0)
	}

	const handleSelectDirectory = async () => {
		try {
			const directory = await window.api.selectDirectory()
			if (directory) {
				setSaveDirectory(directory)
			}
		} catch (error) {
			console.error('Error selecting directory:', error)
		}
	}

	return (
		<div className="flex flex-col min-h-0 h-full">
			{/* 页面标题 */}
			<div className="flex-none">
				<h2 className="text-[#262626] text-xl font-medium">图片格式转换</h2>
				<p className="text-[#8C8C8C] text-sm mt-1">将图片转换为不同格式，支持多种常见图片格式</p>
			</div>

			<div className="flex-1 mt-6 min-h-0">
				<div className="grid grid-cols-1 md:grid-cols-3 gap-0 rounded-lg overflow-hidden bg-white h-full">

					{/* 文件列表区 */}
					<div className="md:col-span-2 border-r border-slate-200 overflow-auto">
						<FileUploadList
							storeType="image"
						/>
					</div>

					{/* 转换设置区 */}
					<div className="md:col-span-1 overflow-auto">
						<Card className="h-full">
							<CardHeader>
								<CardTitle className="text-base font-medium">转换设置</CardTitle>
							</CardHeader>
							<CardContent>
								<ImageConvertForm
									outputFormat={outputFormat}
									onFormatChange={setOutputFormat}
									saveDirectory={saveDirectory}
									onSaveDirectoryChange={setSaveDirectory}
									deleteOriginal={deleteOriginal}
									onDeleteOriginalChange={setDeleteOriginal}
									onSelectDirectory={handleSelectDirectory}
									onConvert={handleConvert}
									onStop={handleStop}
									disabled={files.length === 0 || isProcessing}
									isProcessing={isProcessing}
									progress={progress}
									processedCount={processedCount}
									totalCount={files.length}
								/>
							</CardContent>
						</Card>
					</div>
				</div>
			</div>
		</div>
	)
}
