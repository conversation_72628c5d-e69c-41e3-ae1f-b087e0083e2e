import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ImageWatermarkForm } from '@renderer/components/image/ImageWatermarkForm'
import { FileUploadList } from '@renderer/components/common/FileUploadList'
import { useImageStore } from '@renderer/store/imageStore'

export const Route = createFileRoute('/image/watermark')({
	component: ImageWatermark
})

function ImageWatermark() {
	// 使用 Zustand store 管理文件状态
	const {
		files: storeFiles,
		isProcessing,
		setProcessing
	} = useImageStore()

	// 将 store 中的文件对象转换为路径数组，以兼容现有组件
	const files = storeFiles.map(file => file.path)

	const [watermarkSettings, setWatermarkSettings] = useState({
		type: 'text' as 'text' | 'image',
		text: '版权所有',
		textColor: '#ffffff',
		fontSize: 24,
		opacity: 50,
		position: 'bottom-right',
		imagePath: '',
		tiled: false
	})
	const [saveDirectory, setSaveDirectory] = useState('')
	const [deleteOriginal, setDeleteOriginal] = useState(false)
	const [progress, setProgress] = useState(0)



	const handleSelectWatermarkImage = async () => {
		try {
			// 使用单选模式选择水印图片
			const selectedFiles = await window.api.selectFile('image', false)
			if (selectedFiles && selectedFiles.length > 0) {
				setWatermarkSettings(prev => ({
					...prev,
					imagePath: selectedFiles[0]
				}))
			}
		} catch (error) {
			console.error('Error selecting watermark image:', error)
		}
	}

	const handleApplyWatermark = () => {
		setProcessing(true)
		setProgress(0)

		// 模拟进度更新
		const interval = setInterval(() => {
			setProgress(prev => {
				if (prev >= 100) {
					clearInterval(interval)
					setProcessing(false)
					return 100
				}
				return prev + 5
			})
		}, 200)
	}

	const handleStop = () => {
		setProcessing(false)
		setProgress(0)
	}

	const handleSelectDirectory = async () => {
		try {
			const directory = await window.api.selectDirectory()
			if (directory) {
				setSaveDirectory(directory)
			}
		} catch (error) {
			console.error('Error selecting directory:', error)
		}
	}

	const handleSettingsChange = (settings: Partial<typeof watermarkSettings>) => {
		setWatermarkSettings({ ...watermarkSettings, ...settings })
	}

	const isFormValid = () => {
		if (files.length === 0) return false
		if (watermarkSettings.type === 'text' && !watermarkSettings.text.trim()) return false
		if (watermarkSettings.type === 'image' && !watermarkSettings.imagePath) return false
		return true
	}

	return (
		<div className="flex flex-col min-h-0 h-full">
			{/* 页面标题 */}
			<div className="flex-none">
				<h2 className="text-[#262626] text-xl font-medium">图片水印</h2>
				<p className="text-[#8C8C8C] text-sm mt-1">为图片添加文字或图片水印，支持自定义位置和透明度</p>
			</div>

			<div className="flex-1 mt-6 min-h-0">
				<div className="grid grid-cols-1 md:grid-cols-3 gap-0 rounded-lg overflow-hidden bg-white h-full">
					{/* 文件列表区 */}
					<div className="md:col-span-2 border-r border-slate-200 overflow-auto">
						<FileUploadList
							storeType="image"
						/>
					</div>

					{/* 水印设置区 */}
					<div className="md:col-span-1 overflow-auto">
						<Card className="h-full">
							<CardHeader>
								<CardTitle className="text-base font-medium">水印设置</CardTitle>
							</CardHeader>
							<CardContent>
								<ImageWatermarkForm
									watermarkSettings={watermarkSettings}
									onSettingsChange={handleSettingsChange}
									saveDirectory={saveDirectory}
									onSaveDirectoryChange={setSaveDirectory}
									deleteOriginal={deleteOriginal}
									onDeleteOriginalChange={setDeleteOriginal}
									onSelectDirectory={handleSelectDirectory}
									onSelectWatermarkImage={handleSelectWatermarkImage}
									onApply={handleApplyWatermark}
									onStop={handleStop}
									disabled={!isFormValid() || isProcessing}
									isProcessing={isProcessing}
									progress={progress}
								/>
							</CardContent>
						</Card>
					</div>
				</div>
			</div>
		</div>
	)
}
