import { createRootRoute, Outlet, useRouter } from '@tanstack/react-router'
import { Nav } from '@renderer/components/ui/nav'
import { Toaster } from 'sonner';
import { useEffect } from 'react';
import { useImageStore } from '@renderer/store/imageStore';
import { useVideoStore } from '@renderer/store/videoStore';
import { getSidebarItems } from '@renderer/config/routes';
export const Route = createRootRoute({
	component: () => {
		const router = useRouter();
		const clearImageStore = useImageStore(state => state.clearFiles);
		const clearVideoStore = useVideoStore(state => state.clearFiles);



		// 监听路由变化，清空 store
		useEffect(() => {
			let prevPath = router.state.location.pathname;

			// 订阅路由变化事件
			const unsubscribe = router.history.subscribe(() => {
				const currentPath = router.state.location.pathname;

				// 判断是否跨类别切换（视频/图片）
				const prevCategory = prevPath.includes('/video/') ? 'video' :
					prevPath.includes('/image/') ? 'image' : 'home';
				const currentCategory = currentPath.includes('/video/') ? 'video' :
					currentPath.includes('/image/') ? 'image' : 'home';

				// 只有在跨类别切换时才清空 store
				if (prevCategory !== currentCategory) {
					console.log(`从 ${prevCategory} 切换到 ${currentCategory}，清空 store`);

					// 根据目标类别清空相应的 store
					if (prevCategory === 'video') {
						clearVideoStore();
					} else if (prevCategory === 'image') {
						clearImageStore();
					}
				}

				// 更新前一个路径
				prevPath = currentPath;
			});

			// 组件卸载时取消订阅
			return () => {
				unsubscribe();
			};
		}, [router.history, router.state, clearImageStore, clearVideoStore]);

		// 使用统一配置获取侧边栏菜单项
		const homeItems = getSidebarItems('home')
		const videoItems = getSidebarItems('video')
		const imageItems = getSidebarItems('image')
		const devToolItems = getSidebarItems('dev')
		const textToolItems = getSidebarItems('text')

		// 移除全局拖拽处理器，现在在各个组件中单独处理拖拽
		// useEffect(() => {
		// 	window.api.handleFiles((filePaths) => {
		// 		console.log('拖拽的文件路径:', filePaths);
		// 	});
		// }, []);


		return (
			<div className="w-screen h-screen bg-blue-100 overflow-hidden">
				{/* 顶部标题栏 */}
				{/* <div className="h-12 bg-white shadow-sm flex items-center px-6">
					<Link to="/" className="flex items-center">
						<h1 className="text-[#262626] text-xl font-medium">Media Tool</h1>
					</Link>
				</div> */}

				<div className="w-full h-full flex flex-row">
					<div className="py-8 pl-4 flex flex-col justify-center items-center">

						{/* 左侧导航栏 */}
						<div className="w-56 bg-blue-300 shadow-sm flex flex-col rounded-2xl max-h-full">
							<div className="py-6 overflow-y-auto flex-1">
								<Nav items={homeItems} />

								<Nav
									items={videoItems}
									title="视频工具"
								/>

								<Nav
									items={imageItems}
									title="图片工具"
								/>

								<Nav
									items={devToolItems}
									title="开发工具"
								/>

								<Nav
									items={textToolItems}
									title="文本工具"
								/>
							</div>
						</div>
					</div>

					{/* 主内容区 */}
					<main className="flex-1 p-6 overflow-hidden flex flex-col">
						<Toaster />
						<div className="h-full overflow-auto">
							<Outlet />
						</div>
					</main>
				</div>
			</div>
		)
	}
})
