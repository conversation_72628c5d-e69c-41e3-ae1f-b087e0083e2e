import { createFileRoute } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent } from '@/components/ui/card'
import { GitCompare, Upload, Download, Copy, RefreshCw } from 'lucide-react'
import { toast } from 'sonner'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

export const Route = createFileRoute('/text/diff')({
	component: TextDiff
})

// 简单的差异比较函数
function computeDiff(text1: string, text2: string, ignoreWhitespace: boolean = false): { type: 'add' | 'remove' | 'same', text: string }[] {
	if (!text1 && !text2) return []

	// 将文本分割成行
	const lines1 = text1.split('\n')
	const lines2 = text2.split('\n')

	// 如果忽略空白，则去除每行的前后空白
	if (ignoreWhitespace) {
		for (let i = 0; i < lines1.length; i++) {
			lines1[i] = lines1[i].trim()
		}
		for (let i = 0; i < lines2.length; i++) {
			lines2[i] = lines2[i].trim()
		}
	}

	// 使用最长公共子序列算法计算差异
	const lcs = longestCommonSubsequence(lines1, lines2)

	// 根据 LCS 构建差异结果
	const diff: { type: 'add' | 'remove' | 'same', text: string }[] = []
	let i = 0, j = 0

	for (const line of lcs) {
		// 添加删除的行
		while (i < lines1.length && lines1[i] !== line) {
			diff.push({ type: 'remove', text: lines1[i] })
			i++
		}

		// 添加新增的行
		while (j < lines2.length && lines2[j] !== line) {
			diff.push({ type: 'add', text: lines2[j] })
			j++
		}

		// 添加相同的行
		diff.push({ type: 'same', text: line })
		i++
		j++
	}

	// 添加剩余的删除行
	while (i < lines1.length) {
		diff.push({ type: 'remove', text: lines1[i] })
		i++
	}

	// 添加剩余的新增行
	while (j < lines2.length) {
		diff.push({ type: 'add', text: lines2[j] })
		j++
	}

	return diff
}

// 最长公共子序列算法
function longestCommonSubsequence(a: string[], b: string[]): string[] {
	const m = a.length
	const n = b.length
	const dp: number[][] = Array(m + 1).fill(0).map(() => Array(n + 1).fill(0))

	// 填充 DP 表
	for (let i = 1; i <= m; i++) {
		for (let j = 1; j <= n; j++) {
			if (a[i - 1] === b[j - 1]) {
				dp[i][j] = dp[i - 1][j - 1] + 1
			} else {
				dp[i][j] = Math.max(dp[i - 1][j], dp[i][j - 1])
			}
		}
	}

	// 回溯找出 LCS
	const lcs: string[] = []
	let i = m, j = n
	while (i > 0 && j > 0) {
		if (a[i - 1] === b[j - 1]) {
			lcs.unshift(a[i - 1])
			i--
			j--
		} else if (dp[i - 1][j] > dp[i][j - 1]) {
			i--
		} else {
			j--
		}
	}

	return lcs
}

function TextDiff() {
	const [text1, setText1] = useState('')
	const [text2, setText2] = useState('')
	const [diff, setDiff] = useState<{ type: 'add' | 'remove' | 'same', text: string }[]>([])
	const [ignoreWhitespace, setIgnoreWhitespace] = useState(false)
	const [ignoreCase, setIgnoreCase] = useState(false)
	const [activeTab, setActiveTab] = useState('input')

	useEffect(() => {
		computeDifferences()
	}, [text1, text2, ignoreWhitespace, ignoreCase])

	const computeDifferences = () => {
		let processedText1 = text1
		let processedText2 = text2

		if (ignoreCase) {
			processedText1 = processedText1.toLowerCase()
			processedText2 = processedText2.toLowerCase()
		}

		const differences = computeDiff(processedText1, processedText2, ignoreWhitespace)
		setDiff(differences)
	}

	const handleFileUpload = (side: 1 | 2) => (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0]
		if (!file) return

		const reader = new FileReader()
		reader.onload = (e) => {
			const content = e.target?.result as string
			if (side === 1) {
				setText1(content)
			} else {
				setText2(content)
			}
		}
		reader.onerror = () => {
			toast.error('文件读取失败')
		}
		reader.readAsText(file)
	}

	const copyToClipboard = (text: string) => {
		navigator.clipboard.writeText(text)
			.then(() => toast.success('已复制到剪贴板'))
			.catch(() => toast.error('复制失败'))
	}

	const downloadDiff = () => {
		let diffText = ''

		diff.forEach(line => {
			if (line.type === 'add') {
				diffText += `+ ${line.text}\n`
			} else if (line.type === 'remove') {
				diffText += `- ${line.text}\n`
			} else {
				diffText += `  ${line.text}\n`
			}
		})

		const blob = new Blob([diffText], { type: 'text/plain' })
		const url = URL.createObjectURL(blob)
		const a = document.createElement('a')
		a.href = url
		a.download = 'diff.txt'
		document.body.appendChild(a)
		a.click()
		document.body.removeChild(a)
		URL.revokeObjectURL(url)
	}

	const swapTexts = () => {
		const temp = text1
		setText1(text2)
		setText2(temp)
	}

	const clearTexts = () => {
		setText1('')
		setText2('')
	}

	return (
		<div className="flex flex-col min-h-0 h-full">
			<div className="flex-none">
				<h2 className="text-[#262626] text-xl font-medium">文本对比</h2>
				<p className="text-[#8C8C8C] text-sm mt-1">比较两段文本之间的差异，高亮显示添加和删除的内容</p>
			</div>

			<div className="flex-1 mt-6 min-h-0">
				<div className="h-full bg-white rounded-lg p-6">
					<Tabs defaultValue="input" className="h-full flex flex-col" onValueChange={setActiveTab}>
						<div className="flex justify-between items-center mb-4 flex-none">
							<TabsList>
								<TabsTrigger value="input">输入</TabsTrigger>
								<TabsTrigger value="diff">差异</TabsTrigger>
								<TabsTrigger value="side-by-side">并排对比</TabsTrigger>
							</TabsList>

							<div className="flex items-center space-x-4">
								<div className="flex items-center space-x-2">
									<Checkbox
										id="ignore-whitespace"
										checked={ignoreWhitespace}
										onCheckedChange={(checked) => setIgnoreWhitespace(checked === true)}
									/>
									<Label
										htmlFor="ignore-whitespace"
										className="text-sm font-normal"
									>
										忽略空白
									</Label>
								</div>

								<div className="flex items-center space-x-2">
									<Checkbox
										id="ignore-case"
										checked={ignoreCase}
										onCheckedChange={(checked) => setIgnoreCase(checked === true)}
									/>
									<Label
										htmlFor="ignore-case"
										className="text-sm font-normal"
									>
										忽略大小写
									</Label>
								</div>

								<Button variant="outline" size="sm" onClick={downloadDiff}>
									<Download className="w-4 h-4 mr-1" />
									下载差异
								</Button>
							</div>
						</div>

						<TabsContent value="input" className="flex-1 overflow-hidden">
							<div className="grid grid-cols-1 md:grid-cols-2 gap-6 h-full">
								<Card className="flex flex-col h-full">
									<CardContent className="p-4 flex flex-col h-full">
										<div className="flex justify-between items-center mb-2">
											<Label htmlFor="text1" className="font-medium">原始文本</Label>
											<div className="flex space-x-2">
												<input
													type="file"
													accept=".txt,.md,.json,.js,.ts,.html,.css"
													onChange={handleFileUpload(1)}
													className="hidden"
													id="file-input-1"
												/>
												<label htmlFor="file-input-1">
													<Button variant="outline" size="sm" className="cursor-pointer" asChild>
														<span>
															<Upload className="w-4 h-4 mr-1" />
															上传
														</span>
													</Button>
												</label>
											</div>
										</div>
										<Textarea
											id="text1"
											value={text1}
											onChange={(e) => setText1(e.target.value)}
											placeholder="在此输入原始文本..."
											className="flex-1 min-h-0 font-mono"
										/>
									</CardContent>
								</Card>

								<Card className="flex flex-col h-full">
									<CardContent className="p-4 flex flex-col h-full">
										<div className="flex justify-between items-center mb-2">
											<Label htmlFor="text2" className="font-medium">修改后文本</Label>
											<div className="flex space-x-2">
												<input
													type="file"
													accept=".txt,.md,.json,.js,.ts,.html,.css"
													onChange={handleFileUpload(2)}
													className="hidden"
													id="file-input-2"
												/>
												<label htmlFor="file-input-2">
													<Button variant="outline" size="sm" className="cursor-pointer" asChild>
														<span>
															<Upload className="w-4 h-4 mr-1" />
															上传
														</span>
													</Button>
												</label>
											</div>
										</div>
										<Textarea
											id="text2"
											value={text2}
											onChange={(e) => setText2(e.target.value)}
											placeholder="在此输入修改后文本..."
											className="flex-1 min-h-0 font-mono"
										/>
									</CardContent>
								</Card>
							</div>

							<div className="flex justify-center mt-4 space-x-4">
								<Button variant="outline" onClick={swapTexts}>
									<RefreshCw className="w-4 h-4 mr-1" />
									交换文本
								</Button>
								<Button variant="outline" onClick={clearTexts}>
									清空文本
								</Button>
							</div>
						</TabsContent>

						<TabsContent value="diff" className="flex-1 overflow-hidden">
							<Card className="flex flex-col h-full">
								<CardContent className="p-4 flex flex-col h-full">
									<div className="flex justify-between items-center mb-2">
										<Label className="font-medium">差异结果</Label>
										<Button
											variant="outline"
											size="sm"
											onClick={() => {
												const diffText = diff.map(line => {
													if (line.type === 'add') return `+ ${line.text}`
													if (line.type === 'remove') return `- ${line.text}`
													return `  ${line.text}`
												}).join('\n')
												copyToClipboard(diffText)
											}}
										>
											<Copy className="w-4 h-4 mr-1" />
											复制
										</Button>
									</div>
									<div className="flex-1 min-h-0 overflow-auto p-4 bg-gray-50 rounded border border-gray-200 font-mono whitespace-pre">
										{diff.length > 0 ? (
											<div>
												{diff.map((line, index) => (
													<div
														key={index}
														className={line.type === 'add' ? 'bg-green-100 text-green-800' :
															line.type === 'remove' ? 'bg-red-100 text-red-800' : ''}
													>
														<span className="inline-block w-6">
															{line.type === 'add' ? '+' : line.type === 'remove' ? '-' : ' '}
														</span>
														{line.text}
													</div>
												))}
											</div>
										) : (
											<div className="text-gray-400 text-center">
												{text1 || text2 ? '没有差异' : '请输入要比较的文本'}
											</div>
										)}
									</div>
								</CardContent>
							</Card>
						</TabsContent>

						<TabsContent value="side-by-side" className="flex-1 overflow-hidden">
							<div className="grid grid-cols-1 md:grid-cols-2 gap-6 h-full">
								<Card className="flex flex-col h-full">
									<CardContent className="p-4 flex flex-col h-full">
										<Label className="font-medium mb-2">原始文本</Label>
										<div className="flex-1 min-h-0 overflow-auto p-4 bg-gray-50 rounded border border-gray-200 font-mono whitespace-pre">
											{text1.split('\n').map((line, index) => {
												const isRemoved = diff.some(d => d.type === 'remove' && d.text === line)
												return (
													<div
														key={index}
														className={isRemoved ? 'bg-red-100 text-red-800' : ''}
													>
														{line}
													</div>
												)
											})}
										</div>
									</CardContent>
								</Card>

								<Card className="flex flex-col h-full">
									<CardContent className="p-4 flex flex-col h-full">
										<Label className="font-medium mb-2">修改后文本</Label>
										<div className="flex-1 min-h-0 overflow-auto p-4 bg-gray-50 rounded border border-gray-200 font-mono whitespace-pre">
											{text2.split('\n').map((line, index) => {
												const isAdded = diff.some(d => d.type === 'add' && d.text === line)
												return (
													<div
														key={index}
														className={isAdded ? 'bg-green-100 text-green-800' : ''}
													>
														{line}
													</div>
												)
											})}
										</div>
									</CardContent>
								</Card>
							</div>
						</TabsContent>
					</Tabs>
				</div>
			</div>
		</div>
	)
}
