import { createFileRoute } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent } from '@/components/ui/card'
import { Copy, FileText, Upload, Download } from 'lucide-react'
import { toast } from 'sonner'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Label } from '@/components/ui/label'

export const Route = createFileRoute('/text/markdown')({
	component: MarkdownPreview
})

// 简单的 Markdown 解析函数
function parseMarkdown(markdown: string): string {
	if (!markdown) return ''

	let html = markdown

	// 转义 HTML 标签
	html = html.replace(/</g, '&lt;').replace(/>/g, '&gt;')

	// 标题
	html = html.replace(/^# (.*?)$/gm, '<h1>$1</h1>')
	html = html.replace(/^## (.*?)$/gm, '<h2>$1</h2>')
	html = html.replace(/^### (.*?)$/gm, '<h3>$1</h3>')
	html = html.replace(/^#### (.*?)$/gm, '<h4>$1</h4>')
	html = html.replace(/^##### (.*?)$/gm, '<h5>$1</h5>')
	html = html.replace(/^###### (.*?)$/gm, '<h6>$1</h6>')

	// 粗体和斜体
	html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
	html = html.replace(/\*(.*?)\*/g, '<em>$1</em>')
	html = html.replace(/__(.*?)__/g, '<strong>$1</strong>')
	html = html.replace(/_(.*?)_/g, '<em>$1</em>')

	// 代码块
	html = html.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')

	// 行内代码
	html = html.replace(/`(.*?)`/g, '<code>$1</code>')

	// 链接
	html = html.replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2" target="_blank">$1</a>')

	// 图片
	html = html.replace(/!\[(.*?)\]\((.*?)\)/g, '<img src="$2" alt="$1">')

	// 引用
	html = html.replace(/^> (.*?)$/gm, '<blockquote>$1</blockquote>')

	// 无序列表
	let inList = false
	const lines = html.split('\n')
	for (let i = 0; i < lines.length; i++) {
		if (lines[i].match(/^[\*\-\+] /)) {
			if (!inList) {
				lines[i] = '<ul>\n<li>' + lines[i].replace(/^[\*\-\+] /, '') + '</li>'
				inList = true
			} else {
				lines[i] = '<li>' + lines[i].replace(/^[\*\-\+] /, '') + '</li>'
			}
		} else if (inList) {
			lines[i - 1] += '\n</ul>'
			inList = false
		}
	}
	if (inList) {
		lines[lines.length - 1] += '\n</ul>'
	}
	html = lines.join('\n')

	// 有序列表
	inList = false
	const oLines = html.split('\n')
	for (let i = 0; i < oLines.length; i++) {
		if (oLines[i].match(/^\d+\. /)) {
			if (!inList) {
				oLines[i] = '<ol>\n<li>' + oLines[i].replace(/^\d+\. /, '') + '</li>'
				inList = true
			} else {
				oLines[i] = '<li>' + oLines[i].replace(/^\d+\. /, '') + '</li>'
			}
		} else if (inList) {
			oLines[i - 1] += '\n</ol>'
			inList = false
		}
	}
	if (inList) {
		oLines[oLines.length - 1] += '\n</ol>'
	}
	html = oLines.join('\n')

	// 水平线
	html = html.replace(/^---$/gm, '<hr>')
	html = html.replace(/^\*\*\*$/gm, '<hr>')
	html = html.replace(/^___$/gm, '<hr>')

	// 段落
	html = html.replace(/^(?!<[a-z]|$)(.+)$/gm, '<p>$1</p>')

	// 处理换行
	html = html.replace(/\n\n/g, '<br>')

	return html
}

function MarkdownPreview() {
	const [markdown, setMarkdown] = useState('')
	const [html, setHtml] = useState('')
	const [activeTab, setActiveTab] = useState('edit')

	const defaultMarkdown = `# Markdown 预览工具

## 这是一个二级标题

### 这是一个三级标题

这是一个**粗体**文本，这是一个*斜体*文本。

这是一个 [链接](https://example.com)。

> 这是一个引用

- 这是无序列表项 1
- 这是无序列表项 2
- 这是无序列表项 3

1. 这是有序列表项 1
2. 这是有序列表项 2
3. 这是有序列表项 3

\`\`\`
// 这是一个代码块
function hello() {
  console.log("Hello, world!");
}
\`\`\`

这是一个 \`行内代码\` 示例。

---

![图片描述](https://via.placeholder.com/150)
`

	useEffect(() => {
		if (!markdown && activeTab === 'edit') {
			setMarkdown(defaultMarkdown)
		}

		setHtml(parseMarkdown(markdown))
	}, [markdown, activeTab])

	const copyToClipboard = (text: string, type: string) => {
		navigator.clipboard.writeText(text)
			.then(() => toast.success(`已复制${type}到剪贴板`))
			.catch(() => toast.error('复制失败'))
	}

	const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0]
		if (!file) return

		const reader = new FileReader()
		reader.onload = (e) => {
			const content = e.target?.result as string
			setMarkdown(content)
		}
		reader.onerror = () => {
			toast.error('文件读取失败')
		}
		reader.readAsText(file)
	}

	const downloadMarkdown = () => {
		const blob = new Blob([markdown], { type: 'text/markdown' })
		const url = URL.createObjectURL(blob)
		const a = document.createElement('a')
		a.href = url
		a.download = 'document.md'
		document.body.appendChild(a)
		a.click()
		document.body.removeChild(a)
		URL.revokeObjectURL(url)
	}

	const downloadHtml = () => {
		const fullHtml = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Markdown Preview</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.6;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      color: #333;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
    }
    code {
      background-color: #f5f5f5;
      padding: 2px 4px;
      border-radius: 4px;
    }
    blockquote {
      border-left: 4px solid #ddd;
      padding-left: 16px;
      margin-left: 0;
      color: #666;
    }
    img {
      max-width: 100%;
    }
    a {
      color: #0366d6;
      text-decoration: none;
    }
    a:hover {
      text-decoration: underline;
    }
    h1, h2, h3, h4, h5, h6 {
      margin-top: 24px;
      margin-bottom: 16px;
      font-weight: 600;
      line-height: 1.25;
    }
    h1 {
      font-size: 2em;
      border-bottom: 1px solid #eaecef;
      padding-bottom: 0.3em;
    }
    h2 {
      font-size: 1.5em;
      border-bottom: 1px solid #eaecef;
      padding-bottom: 0.3em;
    }
    h3 {
      font-size: 1.25em;
    }
  </style>
</head>
<body>
  ${html}
</body>
</html>`

		const blob = new Blob([fullHtml], { type: 'text/html' })
		const url = URL.createObjectURL(blob)
		const a = document.createElement('a')
		a.href = url
		a.download = 'document.html'
		document.body.appendChild(a)
		a.click()
		document.body.removeChild(a)
		URL.revokeObjectURL(url)
	}

	return (
		<div className="flex flex-col min-h-0 h-full">
			<div className="flex-none">
				<h2 className="text-[#262626] text-xl font-medium">Markdown 预览</h2>
				<p className="text-[#8C8C8C] text-sm mt-1">编辑 Markdown 文本并实时预览渲染效果</p>
			</div>

			<div className="flex-1 mt-6 min-h-0">
				<div className="h-full bg-white rounded-lg p-6">
					<Tabs defaultValue="edit" className="h-full flex flex-col" onValueChange={setActiveTab}>
						<div className="flex justify-between items-center mb-4 flex-none">
							<TabsList>
								<TabsTrigger value="edit">编辑</TabsTrigger>
								<TabsTrigger value="preview">预览</TabsTrigger>
								<TabsTrigger value="split">分屏</TabsTrigger>
							</TabsList>

							<div className="flex space-x-2">
								<input
									type="file"
									accept=".md,.markdown,.txt"
									onChange={handleFileUpload}
									className="hidden"
									id="markdown-file-input"
								/>
								<label htmlFor="markdown-file-input">
									<Button variant="outline" size="sm" className="cursor-pointer" asChild>
										<span>
											<Upload className="w-4 h-4 mr-1" />
											上传
										</span>
									</Button>
								</label>

								<Button variant="outline" size="sm" onClick={downloadMarkdown}>
									<Download className="w-4 h-4 mr-1" />
									下载 MD
								</Button>

								<Button variant="outline" size="sm" onClick={downloadHtml}>
									<Download className="w-4 h-4 mr-1" />
									下载 HTML
								</Button>
							</div>
						</div>

						<TabsContent value="edit" className="flex-1 overflow-hidden">
							<Card className="flex flex-col h-full">
								<CardContent className="p-4 flex flex-col h-full">
									<div className="flex justify-between items-center mb-2">
										<Label htmlFor="markdown-editor" className="font-medium">Markdown 编辑器</Label>
										<Button
											variant="ghost"
											size="sm"
											onClick={() => copyToClipboard(markdown, 'Markdown')}
										>
											<Copy className="h-4 w-4 mr-1" />
											复制
										</Button>
									</div>
									<Textarea
										id="markdown-editor"
										value={markdown}
										onChange={(e) => setMarkdown(e.target.value)}
										placeholder="在此输入 Markdown 文本..."
										className="flex-1 min-h-0 font-mono"
									/>
								</CardContent>
							</Card>
						</TabsContent>

						<TabsContent value="preview" className="flex-1 overflow-hidden">
							<Card className="flex flex-col h-full">
								<CardContent className="p-4 flex flex-col h-full">
									<div className="flex justify-between items-center mb-2">
										<Label className="font-medium">预览</Label>
										<Button
											variant="ghost"
											size="sm"
											onClick={() => copyToClipboard(html, 'HTML')}
										>
											<Copy className="h-4 w-4 mr-1" />
											复制 HTML
										</Button>
									</div>
									<div
										className="flex-1 min-h-0 overflow-auto p-4 bg-white rounded border border-gray-200"
										dangerouslySetInnerHTML={{ __html: html }}
									/>
								</CardContent>
							</Card>
						</TabsContent>

						<TabsContent value="split" className="flex-1 overflow-hidden">
							<div className="grid grid-cols-1 md:grid-cols-2 gap-6 h-full">
								<Card className="flex flex-col h-full">
									<CardContent className="p-4 flex flex-col h-full">
										<div className="flex justify-between items-center mb-2">
											<Label htmlFor="markdown-editor-split" className="font-medium">Markdown 编辑器</Label>
											<Button
												variant="ghost"
												size="sm"
												onClick={() => copyToClipboard(markdown, 'Markdown')}
											>
												<Copy className="h-4 w-4 mr-1" />
												复制
											</Button>
										</div>
										<Textarea
											id="markdown-editor-split"
											value={markdown}
											onChange={(e) => setMarkdown(e.target.value)}
											placeholder="在此输入 Markdown 文本..."
											className="flex-1 min-h-0 font-mono"
										/>
									</CardContent>
								</Card>

								<Card className="flex flex-col h-full">
									<CardContent className="p-4 flex flex-col h-full">
										<div className="flex justify-between items-center mb-2">
											<Label className="font-medium">预览</Label>
											<Button
												variant="ghost"
												size="sm"
												onClick={() => copyToClipboard(html, 'HTML')}
											>
												<Copy className="h-4 w-4 mr-1" />
												复制 HTML
											</Button>
										</div>
										<div
											className="flex-1 min-h-0 overflow-auto p-4 bg-white rounded border border-gray-200"
											dangerouslySetInnerHTML={{ __html: html }}
										/>
									</CardContent>
								</Card>
							</div>
						</TabsContent>
					</Tabs>
				</div>
			</div>
		</div>
	)
}
