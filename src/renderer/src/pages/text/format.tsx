import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent } from '@/components/ui/card'
import { FileCode, Upload, Download, Copy } from 'lucide-react'
import { toast } from 'sonner'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select"

export const Route = createFileRoute('/text/format')({
	component: CodeFormatter
})

// 简单的代码格式化函数
function formatCode(code: string, language: string, indentSize: number): string {
	if (!code.trim()) return ''

	try {
		switch (language) {
			case 'json':
				return formatJson(code, indentSize)
			case 'html':
				return formatHtml(code, indentSize)
			case 'css':
				return formatCss(code, indentSize)
			case 'js':
			case 'javascript':
				return formatJavaScript(code, indentSize)
			default:
				return code
		}
	} catch (error) {
		console.error('格式化错误:', error)
		throw new Error(`格式化失败: ${error instanceof Error ? error.message : String(error)}`)
	}
}

// JSON 格式化
function formatJson(json: string, indentSize: number): string {
	try {
		const parsed = JSON.parse(json)
		return JSON.stringify(parsed, null, indentSize)
	} catch (error) {
		throw new Error('无效的 JSON 格式')
	}
}

// HTML 格式化 (简单实现)
function formatHtml(html: string, indentSize: number): string {
	const indent = ' '.repeat(indentSize)
	let formatted = ''
	let indentLevel = 0
	let inTag = false
	let inContent = false
	let inComment = false

	for (let i = 0; i < html.length; i++) {
		const char = html[i]
		const nextChar = html[i + 1] || ''

		// 处理注释
		if (char === '<' && nextChar === '!' && html.substr(i, 4) === '<!--') {
			inComment = true
			formatted += '\n' + indent.repeat(indentLevel) + '<!--'
			i += 3
			continue
		}

		if (inComment) {
			if (char === '-' && nextChar === '-' && html[i + 2] === '>') {
				inComment = false
				formatted += '-->'
				i += 2
				continue
			}
			formatted += char
			continue
		}

		// 处理标签开始
		if (char === '<' && nextChar !== '/') {
			inTag = true
			inContent = false
			formatted += '\n' + indent.repeat(indentLevel) + '<'
			indentLevel++
		}
		// 处理标签结束
		else if (char === '<' && nextChar === '/') {
			inTag = true
			inContent = false
			indentLevel--
			formatted += '\n' + indent.repeat(indentLevel) + '<'
		}
		// 处理自闭合标签
		else if (char === '/' && nextChar === '>') {
			inTag = false
			indentLevel--
			formatted += '/>'
			i++
		}
		// 处理标签结束
		else if (char === '>') {
			inTag = false
			inContent = true
			formatted += '>'
		}
		// 处理内容
		else {
			if (inContent && char === '\n') {
				formatted += '\n' + indent.repeat(indentLevel)
			} else {
				formatted += char
			}
		}
	}

	return formatted.trim()
}

// CSS 格式化 (简单实现)
function formatCss(css: string, indentSize: number): string {
	const indent = ' '.repeat(indentSize)
	let formatted = ''
	let indentLevel = 0
	let inSelector = false
	let inProperty = false
	let inComment = false

	// 移除多余空白
	css = css.replace(/\s+/g, ' ').trim()

	for (let i = 0; i < css.length; i++) {
		const char = css[i]
		const nextChar = css[i + 1] || ''

		// 处理注释
		if (char === '/' && nextChar === '*') {
			inComment = true
			formatted += '/*'
			i++
			continue
		}

		if (inComment) {
			if (char === '*' && nextChar === '/') {
				inComment = false
				formatted += '*/'
				i++
				continue
			}
			formatted += char
			continue
		}

		// 处理选择器
		if (char === '{') {
			inSelector = false
			inProperty = true
			formatted += ' {\n' + indent.repeat(indentLevel + 1)
			indentLevel++
		}
		// 处理属性结束
		else if (char === ';') {
			formatted += ';\n' + indent.repeat(indentLevel)
		}
		// 处理块结束
		else if (char === '}') {
			inProperty = false
			inSelector = true
			indentLevel--
			formatted += '\n' + indent.repeat(indentLevel) + '}\n\n' + indent.repeat(indentLevel)
		}
		// 处理属性分隔
		else if (char === ':') {
			formatted += ': '
			i++ // 跳过下一个空格
		}
		else {
			formatted += char
		}
	}

	return formatted.trim()
}

// JavaScript 格式化 (简单实现)
function formatJavaScript(js: string, indentSize: number): string {
	const indent = ' '.repeat(indentSize)
	let formatted = ''
	let indentLevel = 0
	let inString = false
	let stringChar = ''
	let inComment = false
	let inLineComment = false

	for (let i = 0; i < js.length; i++) {
		const char = js[i]
		const nextChar = js[i + 1] || ''
		const prevChar = js[i - 1] || ''

		// 处理字符串
		if ((char === '"' || char === "'" || char === '`') && prevChar !== '\\') {
			if (inString && stringChar === char) {
				inString = false
			} else if (!inString) {
				inString = true
				stringChar = char
			}
			formatted += char
			continue
		}

		if (inString) {
			formatted += char
			continue
		}

		// 处理行注释
		if (char === '/' && nextChar === '/') {
			inLineComment = true
			formatted += '//'
			i++
			continue
		}

		if (inLineComment) {
			if (char === '\n') {
				inLineComment = false
				formatted += '\n' + indent.repeat(indentLevel)
			} else {
				formatted += char
			}
			continue
		}

		// 处理块注释
		if (char === '/' && nextChar === '*') {
			inComment = true
			formatted += '/*'
			i++
			continue
		}

		if (inComment) {
			if (char === '*' && nextChar === '/') {
				inComment = false
				formatted += '*/'
				i++
			} else {
				formatted += char
			}
			continue
		}

		// 处理大括号
		if (char === '{') {
			indentLevel++
			formatted += ' {\n' + indent.repeat(indentLevel)
		}
		else if (char === '}') {
			indentLevel--
			formatted = formatted.trimEnd() + '\n' + indent.repeat(indentLevel) + '}'

			// 如果下一个字符不是分号或右括号，添加换行
			if (nextChar && nextChar !== ';' && nextChar !== ')' && nextChar !== '}') {
				formatted += '\n' + indent.repeat(indentLevel)
			}
		}
		// 处理分号
		else if (char === ';') {
			formatted += ';\n' + indent.repeat(indentLevel)
		}
		// 处理逗号
		else if (char === ',') {
			formatted += ', '

			// 跳过下一个空格
			if (nextChar === ' ') {
				i++
			}
		}
		else if (char === '\n') {
			formatted += '\n' + indent.repeat(indentLevel)
		}
		else {
			formatted += char
		}
	}

	return formatted.trim()
}

function CodeFormatter() {
	const [code, setCode] = useState('')
	const [formattedCode, setFormattedCode] = useState('')
	const [language, setLanguage] = useState('json')
	const [indentSize, setIndentSize] = useState(2)
	const [error, setError] = useState('')

	const handleFormat = () => {
		if (!code.trim()) {
			setFormattedCode('')
			setError('请输入要格式化的代码')
			return
		}

		try {
			const formatted = formatCode(code, language, indentSize)
			setFormattedCode(formatted)
			setError('')
			toast.success('代码格式化成功')
		} catch (err) {
			setError(`格式化错误: ${err instanceof Error ? err.message : String(err)}`)
			toast.error(`格式化失败: ${err instanceof Error ? err.message : String(err)}`)
		}
	}

	const handleMinify = () => {
		if (!code.trim()) {
			setFormattedCode('')
			setError('请输入要压缩的代码')
			return
		}

		try {
			let minified = ''

			switch (language) {
				case 'json':
					minified = JSON.stringify(JSON.parse(code))
					break
				case 'html':
					// 简单的 HTML 压缩
					minified = code
						.replace(/\s+/g, ' ')
						.replace(/>\s+</g, '><')
						.replace(/\s+>/g, '>')
						.replace(/<\s+/g, '<')
						.trim()
					break
				case 'css':
					// 简单的 CSS 压缩
					minified = code
						.replace(/\/\*[\s\S]*?\*\//g, '') // 移除注释
						.replace(/\s+/g, ' ')
						.replace(/\s*{\s*/g, '{')
						.replace(/\s*}\s*/g, '}')
						.replace(/\s*:\s*/g, ':')
						.replace(/\s*;\s*/g, ';')
						.replace(/\s*,\s*/g, ',')
						.trim()
					break
				case 'js':
				case 'javascript':
					// 简单的 JS 压缩 (不处理变量名)
					minified = code
						.replace(/\/\/.*$/gm, '') // 移除行注释
						.replace(/\/\*[\s\S]*?\*\//g, '') // 移除块注释
						.replace(/\s+/g, ' ')
						.replace(/\s*{\s*/g, '{')
						.replace(/\s*}\s*/g, '}')
						.replace(/\s*:\s*/g, ':')
						.replace(/\s*;\s*/g, ';')
						.replace(/\s*,\s*/g, ',')
						.trim()
					break
				default:
					minified = code.replace(/\s+/g, ' ').trim()
			}

			setFormattedCode(minified)
			setError('')
			toast.success('代码压缩成功')
		} catch (err) {
			setError(`压缩错误: ${err instanceof Error ? err.message : String(err)}`)
			toast.error(`压缩失败: ${err instanceof Error ? err.message : String(err)}`)
		}
	}

	const copyToClipboard = () => {
		if (!formattedCode) {
			toast.error('没有可复制的内容')
			return
		}

		navigator.clipboard.writeText(formattedCode)
			.then(() => toast.success('已复制到剪贴板'))
			.catch(() => toast.error('复制失败'))
	}

	const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0]
		if (!file) return

		// 根据文件扩展名设置语言
		const extension = file.name.split('.').pop()?.toLowerCase() || ''
		if (extension === 'json') {
			setLanguage('json')
		} else if (extension === 'html' || extension === 'htm') {
			setLanguage('html')
		} else if (extension === 'css') {
			setLanguage('css')
		} else if (extension === 'js') {
			setLanguage('javascript')
		}

		const reader = new FileReader()
		reader.onload = (e) => {
			const content = e.target?.result as string
			setCode(content)
		}
		reader.onerror = () => {
			toast.error('文件读取失败')
		}
		reader.readAsText(file)
	}

	const downloadFormatted = () => {
		if (!formattedCode) {
			toast.error('没有可下载的内容')
			return
		}

		let extension = 'txt'
		switch (language) {
			case 'json': extension = 'json'; break
			case 'html': extension = 'html'; break
			case 'css': extension = 'css'; break
			case 'javascript': extension = 'js'; break
		}

		const blob = new Blob([formattedCode], { type: 'text/plain' })
		const url = URL.createObjectURL(blob)
		const a = document.createElement('a')
		a.href = url
		a.download = `formatted.${extension}`
		document.body.appendChild(a)
		a.click()
		document.body.removeChild(a)
		URL.revokeObjectURL(url)
	}

	return (
		<div className="flex flex-col min-h-0 h-full">
			<div className="flex-none">
				<h2 className="text-[#262626] text-xl font-medium">代码格式化</h2>
				<p className="text-[#8C8C8C] text-sm mt-1">格式化和美化各种代码，支持 JSON、HTML、CSS 和 JavaScript</p>
			</div>

			<div className="flex-1 mt-6 min-h-0">
				<div className="h-full bg-white rounded-lg p-6">
					<div className="grid grid-cols-1 md:grid-cols-2 gap-6 h-full">
						<Card className="flex flex-col h-full">
							<CardContent className="p-4 flex flex-col h-full">
								<div className="flex justify-between items-center mb-4">
									<div className="flex items-center">
										<Label htmlFor="code-input" className="font-medium mr-4">输入代码</Label>
										<div className="flex items-center space-x-2">
											<Label htmlFor="language-select" className="text-sm">语言:</Label>
											<Select value={language} onValueChange={setLanguage}>
												<SelectTrigger id="language-select" className="w-32">
													<SelectValue placeholder="选择语言" />
												</SelectTrigger>
												<SelectContent>
													<SelectItem value="json">JSON</SelectItem>
													<SelectItem value="html">HTML</SelectItem>
													<SelectItem value="css">CSS</SelectItem>
													<SelectItem value="javascript">JavaScript</SelectItem>
												</SelectContent>
											</Select>
										</div>
										<div className="flex items-center space-x-2 ml-4">
											<Label htmlFor="indent-select" className="text-sm">缩进:</Label>
											<Select
												value={indentSize.toString()}
												onValueChange={(value) => setIndentSize(parseInt(value))}
											>
												<SelectTrigger id="indent-select" className="w-20">
													<SelectValue placeholder="缩进" />
												</SelectTrigger>
												<SelectContent>
													<SelectItem value="2">2 空格</SelectItem>
													<SelectItem value="4">4 空格</SelectItem>
													<SelectItem value="8">8 空格</SelectItem>
												</SelectContent>
											</Select>
										</div>
									</div>
									<div>
										<input
											type="file"
											accept=".json,.html,.htm,.css,.js"
											onChange={handleFileUpload}
											className="hidden"
											id="code-file-input"
										/>
										<label htmlFor="code-file-input">
											<Button variant="outline" size="sm" className="cursor-pointer" asChild>
												<span>
													<Upload className="w-4 h-4 mr-1" />
													上传文件
												</span>
											</Button>
										</label>
									</div>
								</div>
								<Textarea
									id="code-input"
									value={code}
									onChange={(e) => setCode(e.target.value)}
									placeholder={`在此输入要格式化的${language.toUpperCase()}代码...`}
									className="flex-1 min-h-0 font-mono"
								/>
								<div className="flex justify-end space-x-2 mt-4">
									<Button variant="outline" onClick={handleMinify}>压缩</Button>
									<Button onClick={handleFormat}>格式化</Button>
								</div>
							</CardContent>
						</Card>

						<Card className="flex flex-col h-full">
							<CardContent className="p-4 flex flex-col h-full">
								<div className="flex justify-between items-center mb-2">
									<Label className="font-medium">格式化结果</Label>
									<div className="flex space-x-2">
										<Button
											variant="outline"
											size="sm"
											onClick={copyToClipboard}
											disabled={!formattedCode}
										>
											<Copy className="w-4 h-4 mr-1" />
											复制
										</Button>
										<Button
											variant="outline"
											size="sm"
											onClick={downloadFormatted}
											disabled={!formattedCode}
										>
											<Download className="w-4 h-4 mr-1" />
											下载
										</Button>
									</div>
								</div>
								{error ? (
									<div className="flex-1 p-4 bg-red-50 text-red-500 rounded border border-red-200 overflow-auto">
										{error}
									</div>
								) : (
									<pre className="flex-1 p-4 bg-gray-50 rounded border border-gray-200 overflow-auto font-mono">
										{formattedCode || '格式化后的代码将显示在这里...'}
									</pre>
								)}
							</CardContent>
						</Card>
					</div>
				</div>
			</div>
		</div>
	)
}
