import { createFileRoute } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { VideoCompressForm } from '@renderer/components/video/VideoCompressForm'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { MainProcessNoticeType } from '@renderer/types'
import { toast } from 'sonner'
import path from 'path-browserify-esm'
import { FileUploadList } from '@renderer/components/common/FileUploadList'
import { useVideoStore } from '@renderer/store/videoStore'

export const Route = createFileRoute('/video/compress')({
	component: VideoCompress
})

function VideoCompress() {
	// 使用 Zustand store 管理文件状态
	const {
		files: storeFiles,
		currentFile,
		isProcessing,
		setCurrentFile,
		setProcessing,
		setFileStatus
	} = useVideoStore()

	// 将 store 中的文件对象转换为路径数组，以兼容现有组件
	const files = storeFiles.map(file => file.path)

	// 构建文件状态对象，用于传递给 FileList 组件
	const fileProgress: Record<string, number> = {}
	storeFiles.forEach(file => {
		if (file.status !== undefined && typeof file.status === 'number') {
			fileProgress[file.path] = file.status
		}
	})

	const [compressSettings, setCompressSettings] = useState({
		resolution: '720p',
		fps: 30,
		bitrate: 1000,
		saveDirectory: '',
		preset: 'medium',
		deleteOriginal: false
	})
	const [progress, setProgress] = useState(0)

	// 监听主进程通知
	useEffect(() => {
		const unsubscribe = window.api.mainProcessNotice((type, data, filePath) => {
			switch (type) {
				case MainProcessNoticeType.START:
					setCurrentFile(filePath)
					setProcessing(true)
					setProgress(0)
					setFileStatus(filePath, 0)
					toast.info('开始压缩视频', { description: path.basename(filePath) })
					break
				case MainProcessNoticeType.PROGRESS:
					setProgress(data)
					setFileStatus(filePath, data)
					break
				case MainProcessNoticeType.SUCCESS:
					toast.success('视频压缩成功', { description: path.basename(filePath) })
					// 如果是当前处理的文件完成了，标记为完成
					if (currentFile === filePath) {
						setFileStatus(filePath, 100)
						setCurrentFile(null)
						setProgress(0)

						// 如果还有其他文件，继续处理下一个
						const remainingFiles = files.filter(f => f !== currentFile)
						if (remainingFiles.length > 0) {
							compressFile(remainingFiles[0])
						} else {
							setProcessing(false)
							// 所有文件处理完成，显示通知
							toast.success('所有视频压缩完成', { description: `共处理 ${files.length} 个文件` })
						}
					}
					break
				case MainProcessNoticeType.ERROR:
					toast.error('视频压缩失败', { description: data })
					if (currentFile === filePath) {
						setFileStatus(filePath, -1) // -1 表示错误
						setProcessing(false)
						setCurrentFile(null)
						setProgress(0)
					}
					break
				case MainProcessNoticeType.STOP:
					toast.info('视频压缩已停止')
					setProcessing(false)
					setCurrentFile(null)
					setProgress(0)
					break
			}
		})

		return () => {
			unsubscribe()
		}
	}, [files, currentFile, setCurrentFile, setProcessing, setFileStatus])



	// 获取分辨率的宽高
	const getResolutionDimensions = (resolution: string) => {
		const resolutions: Record<string, { width: number, height: number }> = {
			'480p': { width: 854, height: 480 },
			'720p': { width: 1280, height: 720 },
			'1080p': { width: 1920, height: 1080 },
			'1440p': { width: 2560, height: 1440 },
			'2160p': { width: 3840, height: 2160 }
		}
		return resolutions[resolution] || { width: 1280, height: 720 }
	}

	const compressFile = async (filePath: string) => {
		try {
			// 获取分辨率的宽高
			const { width, height } = getResolutionDimensions(compressSettings.resolution)

			// 构建输出路径
			let outputPath = ''
			if (compressSettings.saveDirectory) {
				// 从文件路径中提取文件名
				const fileName = path.basename(filePath, path.extname(filePath))
				const ext = path.extname(filePath)
				// 构建新的输出路径
				outputPath = path.join(compressSettings.saveDirectory, `${fileName}-compressed${ext}`)
			}

			// 调用主进程的压缩函数
			await window.api.compressVideo({
				inputPath: filePath,
				outputPath,
				videoBitrate: `${compressSettings.bitrate}k`,
				width,
				height,
				fps: compressSettings.fps,
				preset: compressSettings.preset,
				deleteOriginal: compressSettings.deleteOriginal
			})
		} catch (error) {
			console.error('Error compressing video:', error)
			toast.error('视频压缩失败', { description: String(error) })
			setProcessing(false)
			setCurrentFile(null)
			setProgress(0)
		}
	}

	const handleCompress = async () => {
		if (files.length === 0) {
			toast.error('请先选择视频文件')
			return
		}

		setProcessing(true)
		// 开始压缩第一个文件
		compressFile(files[0])
	}

	const handleStop = () => {
		window.api.stop()
		setProcessing(false)
		setCurrentFile(null)
		setProgress(0)
	}

	const handleSettingsChange = (settings: Partial<typeof compressSettings>) => {
		setCompressSettings({ ...compressSettings, ...settings })
	}

	const handleSelectDirectory = async () => {
		try {
			const directory = await window.api.selectDirectory()
			if (directory) {
				setCompressSettings({ ...compressSettings, saveDirectory: directory })
			}
		} catch (error) {
			console.error('Error selecting directory:', error)
		}
	}

	return (
		<div className="flex flex-col min-h-0 h-full">
			{/* 页面标题 */}
			<div className="flex-none">
				<h2 className="text-[#262626] text-xl font-medium">视频压缩</h2>
				<p className="text-[#8C8C8C] text-sm mt-1">压缩视频文件大小，支持调整分辨率、帧率等参数</p>
			</div>

			<div className="flex-1 mt-6 min-h-0">
				<div className="grid grid-cols-1 md:grid-cols-3 gap-0 rounded-lg overflow-hidden bg-white h-full">
					{/* 文件列表区 */}
					<div className="md:col-span-2 border-r border-slate-200 overflow-auto">
						<FileUploadList
							storeType="video"
						/>
					</div>

					{/* 压缩设置区 */}
					<div className="md:col-span-1 overflow-auto">
						<Card className="h-full">
							<CardHeader>
								<CardTitle className="text-base font-medium">压缩设置</CardTitle>
							</CardHeader>
							<CardContent>
								<VideoCompressForm
									settings={compressSettings}
									onSettingsChange={handleSettingsChange}
									onSelectDirectory={handleSelectDirectory}
									onCompress={handleCompress}
									onStop={handleStop}
									disabled={files.length === 0 || isProcessing}
									isProcessing={isProcessing}
									progress={progress}
								/>
							</CardContent>
						</Card>
					</div>
				</div>
			</div>
		</div>
	)
}
