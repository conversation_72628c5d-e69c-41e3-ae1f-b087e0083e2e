import { createFileRoute } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { MainProcessNoticeType } from '@renderer/types'
import { toast } from 'sonner'
import path from 'path-browserify-esm'
import { FileUploadList } from '@renderer/components/common/FileUploadList'
import { useVideoStore } from '@renderer/store/videoStore'
import { AudioExtractForm } from '@renderer/components/video/AudioExtractForm'

export const Route = createFileRoute('/video/extract-audio')({
  component: ExtractAudio
})

function ExtractAudio() {
  // 使用 Zustand store 管理文件状态
  const {
    files: storeFiles,
    currentFile,
    isProcessing,
    setCurrentFile,
    setProcessing,
    setFileStatus
  } = useVideoStore()

  // 将 store 中的文件对象转换为路径数组，以兼容现有组件
  const files = storeFiles.map(file => file.path)

  const [progress, setProgress] = useState(0)
  const [audioSettings, setAudioSettings] = useState({
    outputFormat: 'mp3' as 'mp3' | 'aac' | 'wav' | 'flac' | 'ogg',
    bitrate: '192k',
    sampleRate: '44100',
    channels: 2,
    outputDirectory: ''
  })

  // 监听主进程通知
  useEffect(() => {
    const unsubscribe = window.api.mainProcessNotice((type, data, filePath) => {
      switch (type) {
        case MainProcessNoticeType.START:
          setCurrentFile(filePath)
          setProcessing(true)
          setProgress(0)
          setFileStatus(filePath, 0)
          toast.info('开始提取音频', { description: path.basename(filePath) })
          break
        case MainProcessNoticeType.PROGRESS:
          setProgress(data)
          setFileStatus(filePath, data)
          break
        case MainProcessNoticeType.SUCCESS:
          toast.success('音频提取成功', { description: path.basename(filePath) })
          setFileStatus(filePath, 100)
          setCurrentFile(null)
          setProgress(0)
          setProcessing(false)
          break
        case MainProcessNoticeType.ERROR:
          toast.error('音频提取失败', { description: data })
          if (filePath) {
            setFileStatus(filePath, -1)
          }
          setProcessing(false)
          setCurrentFile(null)
          setProgress(0)
          break
        case MainProcessNoticeType.STOP:
          toast.info('音频提取已停止')
          setProcessing(false)
          setCurrentFile(null)
          setProgress(0)
          break
      }
    })

    return () => {
      unsubscribe()
    }
  }, [setCurrentFile, setProcessing, setFileStatus])

  const handleSelectOutputDirectory = async () => {
    try {
      const directory = await window.api.selectDirectory()
      if (directory) {
        setAudioSettings({ ...audioSettings, outputDirectory: directory })
      }
    } catch (error) {
      console.error('Error selecting directory:', error)
    }
  }

  const handleExtractAudio = async () => {
    if (!currentFile) {
      toast.error('请先选择视频文件')
      return
    }

    try {
      setProcessing(true)

      // 构建输出路径
      let outputPath = ''
      if (audioSettings.outputDirectory) {
        // 从文件路径中提取文件名
        const fileName = path.basename(currentFile, path.extname(currentFile))
        const ext = audioSettings.outputFormat
        // 构建新的输出路径
        outputPath = path.join(audioSettings.outputDirectory, `${fileName}.${ext}`)
      }

      // 调用主进程的 extractAudio 函数
      await window.api.extractAudio({
        inputPath: currentFile,
        outputPath,
        format: audioSettings.outputFormat,
        bitrate: audioSettings.bitrate,
        sampleRate: audioSettings.sampleRate,
        channels: audioSettings.channels
      })
    } catch (error) {
      console.error('Error extracting audio:', error)
      toast.error('提取音频失败', { description: String(error) })
      setProcessing(false)
    }
  }

  const handleStop = () => {
    window.api.stop()
    setProcessing(false)
  }

  const handleSettingsChange = (settings: Partial<typeof audioSettings>) => {
    setAudioSettings({ ...audioSettings, ...settings })
  }

  return (
    <div className="flex flex-col min-h-0 h-full">
      {/* 页面标题 */}
      <div className="flex-none">
        <h2 className="text-[#262626] text-xl font-medium">提取视频音频</h2>
        <p className="text-[#8C8C8C] text-sm mt-1">从视频文件中提取音频轨道，支持多种音频格式</p>
      </div>

      <div className="flex-1 mt-6 min-h-0">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-0 rounded-lg overflow-hidden bg-white h-full">
          {/* 文件列表区 */}
          <div className="md:col-span-2 border-r border-slate-200 overflow-auto">
            <FileUploadList
              storeType="video"
            />
          </div>

          {/* 提取设置区 */}
          <div className="md:col-span-1 overflow-auto">
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="text-base font-medium">提取设置</CardTitle>
              </CardHeader>
              <CardContent>
                <AudioExtractForm
                  settings={audioSettings}
                  onSettingsChange={handleSettingsChange}
                  onSelectDirectory={handleSelectOutputDirectory}
                  onExtract={handleExtractAudio}
                  onStop={handleStop}
                  disabled={!currentFile || isProcessing}
                  isProcessing={isProcessing}
                  progress={progress}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
