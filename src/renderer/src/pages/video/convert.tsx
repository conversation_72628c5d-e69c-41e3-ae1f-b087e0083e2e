import { createFileRoute } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { VideoConvertForm } from '@renderer/components/video/VideoConvertForm'
import { Card, CardContent, CardHeader, CardTitle } from '@renderer/components/ui/card'
import { MainProcessNoticeType } from '@renderer/types'
import { toast } from 'sonner'
import path from 'path-browserify-esm'
import { FileUploadList } from '@renderer/components/common/FileUploadList'
import { useVideoStore } from '@renderer/store/videoStore'

export const Route = createFileRoute('/video/convert')({
	component: VideoConvert
})

function VideoConvert() {
	// 使用 Zustand store 管理文件状态
	const {
		files: storeFiles,
		currentFile,
		isProcessing,
		setCurrentFile,
		setProcessing,
		setFileStatus
	} = useVideoStore()

	// 将 store 中的文件对象转换为路径数组，以兼容现有组件
	const files = storeFiles.map(file => file.path)

	// 构建文件状态对象，用于传递给 FileList 组件
	const fileProgress: Record<string, number> = {}
	storeFiles.forEach(file => {
		if (file.status !== undefined && typeof file.status === 'number') {
			fileProgress[file.path] = file.status
		}
	})

	const [outputFormat, setOutputFormat] = useState('mp4')
	const [saveDirectory, setSaveDirectory] = useState('')
	const [deleteOriginal, setDeleteOriginal] = useState(false)
	const [progress, setProgress] = useState(0)

	// 监听主进程通知
	useEffect(() => {
		const unsubscribe = window.api.mainProcessNotice((type, data, filePath) => {
			switch (type) {
				case MainProcessNoticeType.START:
					setCurrentFile(filePath)
					setProcessing(true)
					setProgress(0)
					setFileStatus(filePath, 0)
					toast.info('开始转换视频', { description: path.basename(filePath) })
					break
				case MainProcessNoticeType.PROGRESS:
					setProgress(data)
					setFileStatus(filePath, data)
					break
				case MainProcessNoticeType.SUCCESS:
					toast.success('视频转换成功', { description: path.basename(filePath) })
					// 如果是当前处理的文件完成了，标记为完成
					if (currentFile === filePath) {
						setFileStatus(filePath, 100)
						setCurrentFile(null)
						setProgress(0)

						// 如果还有其他文件，继续处理下一个
						const remainingFiles = files.filter(f => f !== currentFile)
						if (remainingFiles.length > 0) {
							convertFile(remainingFiles[0])
						} else {
							setProcessing(false)
							// 所有文件处理完成，显示通知
							toast.success('所有视频格式转换完成', { description: `共处理 ${files.length} 个文件` })
						}
					}
					break
				case MainProcessNoticeType.ERROR:
					toast.error('视频转换失败', { description: data })
					if (currentFile === filePath) {
						setFileStatus(filePath, -1) // -1 表示错误
						setProcessing(false)
						setCurrentFile(null)
						setProgress(0)
					}
					break
				case MainProcessNoticeType.STOP:
					toast.info('视频转换已停止')
					setProcessing(false)
					setCurrentFile(null)
					setProgress(0)
					break
			}
		})

		return () => {
			unsubscribe()
		}
	}, [files, currentFile, setCurrentFile, setProcessing, setFileStatus])



	const handleSelectDirectory = async () => {
		try {
			const directory = await window.api.selectDirectory()
			if (directory) {
				setSaveDirectory(directory)
			}
		} catch (error) {
			console.error('Error selecting directory:', error)
		}
	}

	const convertFile = async (filePath: string) => {
		try {
			// 构建输出路径
			let outputPath = ''
			if (saveDirectory) {
				// 从文件路径中提取文件名
				const fileName = path.basename(filePath, path.extname(filePath))
				// 构建新的输出路径
				outputPath = path.join(saveDirectory, `${fileName}.${outputFormat}`)
			}

			// 调用主进程的转换函数
			await window.api.convertVideoFormat({
				inputPath: filePath,
				outputPath,
				format: outputFormat,
				deleteOriginal
			})
		} catch (error) {
			console.error('Error converting video:', error)
			toast.error('视频转换失败', { description: String(error) })
			setProcessing(false)
			setCurrentFile(null)
			setProgress(0)
		}
	}

	const handleConvert = async () => {
		if (files.length === 0) {
			toast.error('请先选择视频文件')
			return
		}

		setProcessing(true)
		// 开始转换第一个文件
		convertFile(files[0])
	}

	const handleStop = () => {
		window.api.stop()
		setProcessing(false)
		setCurrentFile(null)
		setProgress(0)
	}

	return (
		<div className="flex flex-col min-h-0 h-full">
			{/* 页面标题 */}
			<div className="flex-none">
				<h2 className="text-[#262626] text-xl font-medium">视频格式转换</h2>
				<p className="text-[#8C8C8C] text-sm mt-1">将视频转换为不同格式，支持多种常见视频格式</p>
			</div>

			<div className="flex-1 mt-6 min-h-0">
				<div className="grid grid-cols-1 md:grid-cols-3 gap-0 rounded-lg overflow-hidden bg-white h-full">
					{/* 文件列表区 */}
					<div className="md:col-span-2 border-r border-slate-200 overflow-auto">
						<FileUploadList
							storeType="video"
						/>
					</div>

					{/* 转换设置区 */}
					<div className="md:col-span-1 overflow-auto">
						<Card className="h-full">
							<CardHeader>
								<CardTitle className="text-base font-medium">转换设置</CardTitle>
							</CardHeader>
							<CardContent>
								<VideoConvertForm
									outputFormat={outputFormat}
									onFormatChange={setOutputFormat}
									saveDirectory={saveDirectory}
									onSaveDirectoryChange={setSaveDirectory}
									deleteOriginal={deleteOriginal}
									onDeleteOriginalChange={setDeleteOriginal}
									onSelectDirectory={handleSelectDirectory}
									onConvert={handleConvert}
									onStop={handleStop}
									disabled={files.length === 0 || isProcessing}
									isProcessing={isProcessing}
									progress={progress}
								/>
							</CardContent>
						</Card>
					</div>
				</div>
			</div>
		</div>
	)
}
