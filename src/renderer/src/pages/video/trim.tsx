import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { VideoTrimForm } from '@renderer/components/video/VideoTrimForm'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { FileUploadList } from '@renderer/components/common/FileUploadList'
import { useVideoStore } from '@renderer/store/videoStore'

export const Route = createFileRoute('/video/trim')({
	component: VideoTrim
})

function VideoTrim() {
	// 使用 Zustand store 管理文件状态
	const {
		files: storeFiles,
		currentFile,
		isProcessing,
		addFiles,
		removeFile,
		setCurrentFile,
		setProcessing
	} = useVideoStore()

	// 将 store 中的文件对象转换为路径数组，以兼容现有组件
	const files = storeFiles.map(file => file.path)

	const [trimSettings, setTrimSettings] = useState({
		startTime: '00:00:00',
		endTime: '00:01:00',
		saveDirectory: ''
	})

	// 处理打开文件所在目录
	const handleOpenDirectory = (filePath: string) => {
		window.api.openFileDirectory(filePath)
	}

	const handleTrim = () => {
		setProcessing(true)
		// 实际截取逻辑将在这里实现
		setTimeout(() => {
			setProcessing(false)
		}, 2000)
	}

	const handleSettingsChange = (settings: Partial<typeof trimSettings>) => {
		setTrimSettings({ ...trimSettings, ...settings })
	}

	const handleSelectDirectory = async () => {
		try {
			const directory = await window.api.selectDirectory()
			if (directory) {
				setTrimSettings({ ...trimSettings, saveDirectory: directory })
			}
		} catch (error) {
			console.error('Error selecting directory:', error)
		}
	}

	return (
		<div className="flex flex-col min-h-0 h-full">
			{/* 页面标题 */}
			<div className="flex-none">
				<h2 className="text-[#262626] text-xl font-medium">视频截取</h2>
				<p className="text-[#8C8C8C] text-sm mt-1">截取视频片段，保存为新的视频文件</p>
			</div>

			<div className="flex-1 mt-6 min-h-0">
				<div className="grid grid-cols-1 md:grid-cols-3 gap-0 rounded-lg overflow-hidden bg-white h-full">
					{/* 文件列表区 */}
					<div className="md:col-span-2 border-r border-slate-200 overflow-auto">
						<FileUploadList
							storeType="video"
						/>
					</div>

					{/* 截取设置区 */}
					<div className="md:col-span-1 overflow-auto">
						<Card className="h-full">
							<CardHeader>
								<CardTitle className="text-base font-medium">截取设置</CardTitle>
							</CardHeader>
							<CardContent>
								<VideoTrimForm
									settings={trimSettings}
									onSettingsChange={handleSettingsChange}
									onSelectDirectory={handleSelectDirectory}
									onTrim={handleTrim}
									selectedFile={currentFile}
									disabled={!currentFile || !trimSettings.saveDirectory || isProcessing}
								/>
							</CardContent>
						</Card>
					</div>
				</div>
			</div>
		</div>
	)
}
