import { createFileRoute } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { VideoInfoDisplay } from '@renderer/components/video/VideoInfoDisplay'
import { MultiVideoDisplay } from '@renderer/components/video/MultiVideoDisplay'
import { useVideoStore } from '@renderer/store/videoStore'

export const Route = createFileRoute('/video/info')({
  component: VideoInfo
})

interface VideoMetadata {
  format: {
    filename: string;
    duration: string;
    size: string;
    bit_rate: string;
  };
  streams: {
    codec_type: string;
    codec_name: string;
    width?: number;
    height?: number;
    display_aspect_ratio?: string;
    r_frame_rate?: string;
    avg_frame_rate?: string;
    bit_rate?: string;
    channels?: number;
    channel_layout?: string;
    sample_rate?: string;
  }[];
}

function VideoInfo() {
  // 使用 Zustand store 管理文件状态
  const {
    files,
    currentFile
  } = useVideoStore()

  // 单文件模式的状态
  const [isLoading, setIsLoading] = useState(false)
  const [videoInfo, setVideoInfo] = useState<VideoMetadata | null>(null)
  const [error, setError] = useState<string | null>(null)

  // 多文件模式的状态
  const [videoInfoMap, setVideoInfoMap] = useState<Map<string, VideoMetadata | null>>(new Map())
  const [loadingMap, setLoadingMap] = useState<Map<string, boolean>>(new Map())
  const [errorMap, setErrorMap] = useState<Map<string, string | null>>(new Map())

  // 清空视频信息
  const clearVideoInfo = () => {
    setVideoInfo(null)
    setError(null)
    // 清空多文件状态
    setVideoInfoMap(new Map())
    setLoadingMap(new Map())
    setErrorMap(new Map())
    // 清空当前选中的文件
    const { clearFiles } = useVideoStore.getState()
    clearFiles()
  }

  const getVideoInfo = async (filePath: string = currentFile || '') => {
    if (!filePath) return;

    setIsLoading(true);
    setError(null);

    try {
      // 调用主进程的 getVideoInfo 函数获取视频信息
      const info = await window.api.getVideoInfo(filePath);
      setVideoInfo(info);
    } catch (err) {
      console.error('Error getting video info:', err);
      setError('获取视频信息失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 多文件模式获取视频信息
  const getMultiVideoInfo = async (filePath: string) => {
    if (!filePath) return;

    // 更新loading状态
    setLoadingMap(prev => new Map(prev.set(filePath, true)));
    setErrorMap(prev => new Map(prev.set(filePath, null)));

    try {
      // 调用主进程的 getVideoInfo 函数获取视频信息
      const info = await window.api.getVideoInfo(filePath);
      setVideoInfoMap(prev => new Map(prev.set(filePath, info)));
    } catch (err) {
      console.error('Error getting video info:', err);
      setErrorMap(prev => new Map(prev.set(filePath, '获取视频信息失败，请重试')));
    } finally {
      setLoadingMap(prev => new Map(prev.set(filePath, false)));
    }
  };

  // 选择文件（支持多选）
  const selectFiles = () => {
    window.api.selectFile('video', true).then(selectedFiles => {
      if (selectedFiles && selectedFiles.length > 0) {
        // 添加新选择的文件到现有文件列表
        const { addFiles } = useVideoStore.getState();
        addFiles(selectedFiles);
      }
    }).catch(err => {
      console.error('Error selecting files:', err);
    });
  };

  // 当 currentFile 变化时自动获取视频信息（单文件模式）
  useEffect(() => {
    if (currentFile && files.length === 1) {
      getVideoInfo(currentFile);
    }
  }, [currentFile, files.length]);

  // 当文件列表变化时自动获取视频信息（包括单文件和多文件模式）
  useEffect(() => {
    if (files.length >= 1) {
      files.forEach(file => {
        // 只获取还没有信息的文件
        if (!videoInfoMap.has(file.path) && !loadingMap.get(file.path)) {
          getMultiVideoInfo(file.path);
        }
      });
    }
  }, [files, videoInfoMap, loadingMap]);

  // 判断是单文件模式还是多文件模式
  const isMultiFileMode = files.length > 1;

  return (
    <div className="flex flex-col min-h-0 h-full">
      {/* 页面标题 */}
      <div className="flex-none">
        <h2 className="text-[#262626] text-xl font-medium">视频信息</h2>
        <p className="text-[#8C8C8C] text-sm mt-1">查看视频文件的详细信息，包括分辨率、帧率等参数</p>
      </div>

      {/* 视频信息显示区 */}
      <div className="flex-1 mt-6 min-h-0">
        <div className="flex flex-col bg-white rounded-lg h-full overflow-auto">
          {isMultiFileMode ? (
            /* 多文件模式 */
            <MultiVideoDisplay
              videoInfoMap={videoInfoMap}
              loadingMap={loadingMap}
              errorMap={errorMap}
              onGetVideoInfo={getMultiVideoInfo}
              onSelectFiles={selectFiles}
              onClear={clearVideoInfo}
            />
          ) : (
            /* 单文件模式 */
            <VideoInfoDisplay
              videoInfo={videoInfo}
              isLoading={isLoading}
              error={error}
              selectedFile={currentFile}
              onSelectFile={selectFiles}
              onGetInfo={() => getVideoInfo()} // 保留重试功能
              onClear={clearVideoInfo} // 添加清空功能
            />
          )}
        </div>
      </div>
    </div>
  )
}
