import { create } from 'zustand'
import { toast } from 'sonner'

interface ImageFile {
	path: string
	status?: number | string // 处理状态：数字表示进度百分比，字符串表示状态描述
}

interface ImageStore {
	// 状态
	files: ImageFile[]
	currentFile: string | null
	isProcessing: boolean

	// 操作
	addFiles: (filePaths: string[]) => void
	removeFile: (filePath: string) => void
	clearFiles: () => void
	setCurrentFile: (filePath: string | null) => void
	setProcessing: (isProcessing: boolean) => void
	setFileStatus: (filePath: string, status: number | string) => void
	resetFileStatus: () => void
}

export const useImageStore = create<ImageStore>((set, get) => ({
	// 初始状态
	files: [],
	currentFile: null,
	isProcessing: false,

	// 操作
	addFiles: (filePaths) => set((state) => {
		// 检查重复文件
		const duplicateFiles: string[] = []
		const newFiles: ImageFile[] = []

		filePaths.forEach((filePath) => {
			if (state.files.some(file => file.path === filePath)) {
				duplicateFiles.push(filePath)
			} else {
				newFiles.push({ path: filePath })
			}
		})

		// 显示提示信息
		if (duplicateFiles.length > 0) {
			const fileNames = duplicateFiles.map(file => file.split('/').pop() || file.split('\\').pop())
			toast.info(`已跳过${duplicateFiles.length}个重复文件: ${fileNames.join(', ')}`)
		}

		return {
			files: [...state.files, ...newFiles],
			// 如果当前没有选中文件，则选中第一个新文件
			currentFile: state.currentFile || (newFiles.length > 0 ? newFiles[0].path : null)
		}
	}),

	removeFile: (filePath) => set((state) => {
		// 如果正在处理该文件，不允许移除
		if (state.isProcessing && state.currentFile === filePath) {
			toast.error('无法移除正在处理的文件')
			return state
		}

		const newFiles = state.files.filter(file => file.path !== filePath)
		let newCurrentFile = state.currentFile

		// 如果删除的是当前选中的文件，则重新选择
		if (state.currentFile === filePath) {
			newCurrentFile = newFiles.length > 0 ? newFiles[0].path : null
		}

		return {
			files: newFiles,
			currentFile: newCurrentFile
		}
	}),

	clearFiles: () => {
		console.log('清空 imageStore');
		return set({ files: [], currentFile: null });
	},

	setCurrentFile: (filePath) => {
		// 获取当前状态
		const state = get();

		// 如果传入的文件路径为 null，或者该文件存在于文件列表中，则直接设置
		if (filePath === null || state.files.some(file => file.path === filePath)) {
			set({ currentFile: filePath });
			return;
		}

		// 如果传入的文件路径不存在于文件列表中，则选择第一个文件（如果有）
		if (state.files.length > 0) {
			set({ currentFile: state.files[0].path });
		} else {
			set({ currentFile: null });
		}
	},

	setProcessing: (isProcessing) => set({ isProcessing }),

	setFileStatus: (filePath, status) => {
		console.log("🚀 ~ filePath, status:", filePath, status)
		set((state) => ({
			files: state.files.map(file =>
				file.path === filePath ? { ...file, status } : file
			)
		}))
	},
	resetFileStatus: () => set((state) => ({
		files: state.files.map(file => ({ path: file.path }))
	}))
}))
