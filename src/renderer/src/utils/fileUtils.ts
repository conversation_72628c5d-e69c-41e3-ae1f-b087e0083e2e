/**
 * 从文件路径中提取文件名
 * @param filePath 文件路径
 * @returns 文件名
 */
export const getFileName = (filePath: string): string => {
	// 处理不同操作系统的路径分隔符
	const normalizedPath = filePath.replace(/\\/g, '/');
	return normalizedPath.split('/').pop() || filePath;
};

/**
 * 从文件名中提取文件扩展名
 * @param fileName 文件名
 * @returns 文件扩展名（小写）
 */
export const getFileExtension = (fileName: string): string => {
	return fileName.split('.').pop()?.toLowerCase() || '';
};

/**
 * 格式化文件大小
 * @param bytes 文件大小（字节或字符串）
 * @returns 格式化后的文件大小
 */
export const formatFileSize = (bytes: number | string): string => {
	// 如果是字符串，尝试转换为数字
	if (typeof bytes === 'string') {
		// 如果已经是格式化的大小字符串（如 "10.5 MB"），直接返回
		if (bytes.includes('Bytes') || bytes.includes('KB') ||
			bytes.includes('MB') || bytes.includes('GB') ||
			bytes.includes('TB')) {
			return bytes;
		}

		// 尝试提取数字
		const match = bytes.match(/(\d+(\.\d+)?)/);
		if (match) {
			bytes = parseFloat(match[1]);
		} else {
			return bytes; // 无法解析，返回原始字符串
		}
	}

	if (bytes === 0) return '0 Bytes';

	const k = 1024;
	const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
	const i = Math.floor(Math.log(bytes) / Math.log(k));

	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 格式化视频时长
 * @param seconds 视频时长（秒或字符串）
 * @returns 格式化后的视频时长 (HH:MM:SS)
 */
export const formatDuration = (seconds: number | string): string => {
	// 如果是字符串，尝试转换为数字
	if (typeof seconds === 'string') {
		// 如果已经是格式化的时间字符串（如 "01:30:45"），直接返回
		if (seconds.includes(':')) {
			return seconds;
		}

		// 尝试提取数字
		const match = seconds.match(/(\d+(\.\d+)?)/);
		if (match) {
			seconds = parseFloat(match[1]);
		} else {
			return seconds; // 无法解析，返回原始字符串
		}
	}

	const hours = Math.floor(seconds / 3600);
	const minutes = Math.floor((seconds % 3600) / 60);
	const secs = Math.floor(seconds % 60);

	return [
		hours.toString().padStart(2, '0'),
		minutes.toString().padStart(2, '0'),
		secs.toString().padStart(2, '0')
	].join(':');
};

/**
 * 格式化比特率
 * @param bps 比特率（bps或字符串）
 * @returns 格式化后的比特率
 */
export const formatBitrate = (bps: number | string): string => {
	// 如果是字符串，尝试提取数字部分
	if (typeof bps === 'string') {
		// 如果已经是格式化的字符串（如 "1500 kb/s"），直接返回
		if (bps.includes('kb/s') || bps.includes('Kbps') ||
			bps.includes('Mbps') || bps.includes('mb/s') ||
			bps.includes('bps')) {
			return bps;
		}

		// 尝试提取数字
		const match = bps.match(/(\d+)/);
		if (match) {
			bps = parseInt(match[1], 10);
		} else {
			return bps; // 无法解析，返回原始字符串
		}
	}

	if (bps < 1000) {
		return `${bps} bps`;
	} else if (bps < 1000000) {
		return `${(bps / 1000).toFixed(2)} Kbps`;
	} else {
		return `${(bps / 1000000).toFixed(2)} Mbps`;
	}
};

/**
 * 支持的视频文件扩展名列表
 */
export const VIDEO_EXTENSIONS = ['mp4', 'avi', 'mkv', 'mov', 'webm', 'flv', 'ts', 'mts', 'm2ts', 'wmv', 'asf', '3gp', 'm4v'];

/**
 * 支持的图片文件扩展名列表
 */
export const IMAGE_EXTENSIONS = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];

/**
 * 检查文件是否为视频文件
 * @param filePath 文件路径
 * @returns 是否为视频文件
 */
export const isVideoFile = (filePath: string): boolean => {
	const ext = getFileExtension(getFileName(filePath));
	return VIDEO_EXTENSIONS.includes(ext);
};

/**
 * 检查文件是否为图片文件
 * @param filePath 文件路径
 * @returns 是否为图片文件
 */
export const isImageFile = (filePath: string): boolean => {
	const ext = getFileExtension(getFileName(filePath));
	return IMAGE_EXTENSIONS.includes(ext);
};

/**
 * 格式化帧率
 * @param frameRate 帧率字符串（如 "30/1"）
 * @returns 格式化后的帧率
 */
export const formatFrameRate = (frameRate: string): string => {
	if (!frameRate) return '';

	// 如果是分数形式（如 "30/1"）
	if (frameRate.includes('/')) {
		const [numerator, denominator] = frameRate.split('/').map(Number);
		if (denominator && denominator !== 0) {
			return `${(numerator / denominator).toFixed(2)} fps`;
		}
		return `${numerator} fps`;
	}

	// 如果已经是数字
	const match = frameRate.match(/(\d+(\.\d+)?)/);
	if (match) {
		return `${parseFloat(match[1]).toFixed(2)} fps`;
	}

	return frameRate;
};

/**
 * 格式化视频编解码器名称
 * @param codecName 编解码器名称
 * @returns 格式化后的编解码器名称
 */
export const formatCodecName = (codecName: string): string => {
	const codecMap: Record<string, string> = {
		'h264': 'H.264 / AVC',
		'hevc': 'H.265 / HEVC',
		'h265': 'H.265 / HEVC',
		'vp8': 'VP8',
		'vp9': 'VP9',
		'av1': 'AV1',
		'mpeg2video': 'MPEG-2',
		'mpeg4': 'MPEG-4',
		'mjpeg': 'Motion JPEG',
		'aac': 'AAC',
		'mp3': 'MP3',
		'opus': 'Opus',
		'vorbis': 'Vorbis',
		'flac': 'FLAC',
		'pcm_s16le': 'PCM 16-bit',
		'pcm_s24le': 'PCM 24-bit',
		'ac3': 'Dolby Digital (AC-3)',
		'eac3': 'Dolby Digital Plus (E-AC-3)',
		'dts': 'DTS',
		'truehd': 'Dolby TrueHD'
	};

	return codecMap[codecName.toLowerCase()] || codecName;
};
