import React from 'react';
import {
	FileVideo,
	FileImage,
	FileAudio,
	File,
	FileText,
	FileSpreadsheet,
	FileCode,
	Video,
	Image,
	Music,
	Archive
} from 'lucide-react';

// 文件格式到图标的映射
const formatIconMap: Record<string, React.ReactNode> = {
	// 视频格式
	mp4: <Video className="text-[#1890FF]" size={16} />,
	avi: <Video className="text-[#52C41A]" size={16} />,
	mkv: <FileVideo className="text-[#722ED1]" size={16} />,
	mov: <Video className="text-[#FA8C16]" size={16} />,
	webm: <Video className="text-[#EB2F96]" size={16} />,
	flv: <FileVideo className="text-[#F5222D]" size={16} />,

	// 图片格式
	jpg: <Image className="text-[#1890FF]" size={16} />,
	jpeg: <Image className="text-[#1890FF]" size={16} />,
	png: <Image className="text-[#52C41A]" size={16} />,
	gif: <Image className="text-[#FA8C16]" size={16} />,
	webp: <Image className="text-[#722ED1]" size={16} />,
	bmp: <FileImage className="text-[#EB2F96]" size={16} />,

	// 音频格式
	mp3: <Music className="text-[#1890FF]" size={16} />,
	wav: <FileAudio className="text-[#52C41A]" size={16} />,
	ogg: <FileAudio className="text-[#FA8C16]" size={16} />,

	// 文档格式
	pdf: <FileText className="text-[#F5222D]" size={16} />,
	doc: <FileText className="text-[#1890FF]" size={16} />,
	docx: <FileText className="text-[#1890FF]" size={16} />,
	xls: <FileSpreadsheet className="text-[#52C41A]" size={16} />,
	xlsx: <FileSpreadsheet className="text-[#52C41A]" size={16} />,
	ppt: <FileText className="text-[#FA8C16]" size={16} />,
	pptx: <FileText className="text-[#FA8C16]" size={16} />,

	// 压缩文件
	zip: <Archive className="text-[#FAAD14]" size={16} />,
	rar: <Archive className="text-[#FAAD14]" size={16} />,
	'7z': <Archive className="text-[#FAAD14]" size={16} />,

	// 代码文件
	js: <FileCode className="text-[#F7DF1E]" size={16} />,
	jsx: <FileCode className="text-[#3178C6]" size={16} />,
	ts: <FileCode className="text-[#3178C6]" size={16} />,
	tsx: <FileCode className="text-[#3178C6]" size={16} />,
	html: <FileCode className="text-[#E34F26]" size={16} />,
	css: <FileCode className="text-[#1572B6]" size={16} />,

	// 默认
	default: <File className="text-[#8C8C8C]" size={16} />
};

// 视频格式列表
export const videoFormats = ['mp4', 'avi', 'mkv', 'mov', 'webm', 'flv'];

// 图片格式列表
export const imageFormats = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];

// 获取文件格式的图标
export const getFormatIcon = (fileName: string): React.ReactNode => {
	const extension = fileName.split('.').pop()?.toLowerCase() || '';
	return formatIconMap[extension] || formatIconMap.default;
};

// 判断文件是否为视频
export const isVideoFile = (fileName: string): boolean => {
	const extension = fileName.split('.').pop()?.toLowerCase() || '';
	return videoFormats.includes(extension);
};

// 判断文件是否为图片
export const isImageFile = (fileName: string): boolean => {
	const extension = fileName.split('.').pop()?.toLowerCase() || '';
	return imageFormats.includes(extension);
};

// 格式化文件大小
export const formatFileSize = (bytes: number): string => {
	if (bytes === 0) return '0 Bytes';

	const k = 1024;
	const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
	const i = Math.floor(Math.log(bytes) / Math.log(k));

	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
