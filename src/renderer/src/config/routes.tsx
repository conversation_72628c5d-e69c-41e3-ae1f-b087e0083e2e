import {
	Home, FileOutput, FileVideo, Scissors, FileImage, Upload, Info, Headphones,
	Code, FileJson, Hash, FileCode, FileText, GitCompare, TerminalSquare, Image
} from 'lucide-react'
import { LucideIcon } from 'lucide-react'

// 定义工具类型
export type ToolCategory = 'home' | 'video' | 'image' | 'dev' | 'text'

// 定义工具项接口
export interface ToolItem {
	id: string
	title: string
	description: string
	path: string
	icon: LucideIcon
	category: ToolCategory
	color?: string // 工具卡片图标颜色
}

// 定义分类接口
export interface CategoryConfig {
	id: ToolCategory
	title: string
	tools: ToolItem[]
}

// 所有工具的统一配置
export const toolsConfig: ToolItem[] = [
	// 首页
	{
		id: 'home',
		title: '首页',
		description: '媒体工具首页',
		path: '/',
		icon: Home,
		category: 'home',
		color: '#1890FF'
	},

	// 视频工具
	{
		id: 'video-info',
		title: '视频信息',
		description: '查看视频文件的详细信息',
		path: '/video/info',
		icon: Info,
		category: 'video',
		color: '#722ED1'
	},
	{
		id: 'video-convert',
		title: '格式转换',
		description: '将视频转换为不同格式',
		path: '/video/convert',
		icon: FileOutput,
		category: 'video',
		color: '#1890FF'
	},
	{
		id: 'video-compress',
		title: '视频压缩',
		description: '压缩视频文件大小',
		path: '/video/compress',
		icon: FileVideo,
		category: 'video',
		color: '#52C41A'
	},
	{
		id: 'video-trim',
		title: '视频截取',
		description: '截取视频片段',
		path: '/video/trim',
		icon: Scissors,
		category: 'video',
		color: '#FAAD14'
	},
	{
		id: 'video-extract-audio',
		title: '提取音频',
		description: '从视频中提取音频轨道',
		path: '/video/extract-audio',
		icon: Headphones,
		category: 'video',
		color: '#EB2F96'
	},

	// 图片工具
	{
		id: 'image-compress',
		title: '图片压缩',
		description: '压缩图片文件大小',
		path: '/image/compress',
		icon: FileImage,
		category: 'image',
		color: '#1890FF'
	},
	{
		id: 'image-enhance',
		title: '清晰度提升',
		description: '提高图片分辨率和清晰度',
		path: '/image/enhance',
		icon: Image,
		category: 'image',
		color: '#EB2F96'
	},
	{
		id: 'image-convert',
		title: '格式转换',
		description: '将图片转换为不同格式',
		path: '/image/convert',
		icon: FileOutput,
		category: 'image',
		color: '#52C41A'
	},
	{
		id: 'image-watermark',
		title: '添加水印',
		description: '为图片添加文字或图片水印',
		path: '/image/watermark',
		icon: Image,
		category: 'image',
		color: '#722ED1'
	},
	{
		id: 'image-upload',
		title: '图床上传',
		description: '将图片上传至图床',
		path: '/image/upload',
		icon: Upload,
		category: 'image',
		color: '#FAAD14'
	},

	// 开发工具
	{
		id: 'dev-json',
		title: 'JSON 格式化',
		description: '格式化或压缩 JSON 数据',
		path: '/dev/json',
		icon: FileJson,
		category: 'dev',
		color: '#1890FF'
	},
	{
		id: 'dev-base64',
		title: 'Base64 编解码',
		description: '编码或解码 Base64 数据',
		path: '/dev/base64',
		icon: Hash,
		category: 'dev',
		color: '#722ED1'
	},
	{
		id: 'dev-url',
		title: 'URL 编解码',
		description: '编码或解码 URL 数据',
		path: '/dev/url',
		icon: Code,
		category: 'dev',
		color: '#52C41A'
	},
	{
		id: 'dev-regex',
		title: '正则表达式',
		description: '测试和验证正则表达式',
		path: '/dev/regex',
		icon: TerminalSquare,
		category: 'dev',
		color: '#FAAD14'
	},

	// 文本工具
	{
		id: 'text-markdown',
		title: 'Markdown 预览',
		description: '编辑和预览 Markdown 文本',
		path: '/text/markdown',
		icon: FileText,
		category: 'text',
		color: '#EB2F96'
	},
	{
		id: 'text-diff',
		title: '文本对比',
		description: '比较两段文本的差异',
		path: '/text/diff',
		icon: GitCompare,
		category: 'text',
		color: '#1890FF'
	},
	{
		id: 'text-format',
		title: '代码格式化',
		description: '格式化各种编程语言的代码',
		path: '/text/format',
		icon: FileCode,
		category: 'text',
		color: '#52C41A'
	}
]

// 分类配置
export const categoriesConfig: CategoryConfig[] = [
	{
		id: 'home',
		title: '首页',
		tools: toolsConfig.filter(tool => tool.category === 'home')
	},
	{
		id: 'video',
		title: '视频工具',
		tools: toolsConfig.filter(tool => tool.category === 'video')
	},
	{
		id: 'image',
		title: '图片工具',
		tools: toolsConfig.filter(tool => tool.category === 'image')
	},
	{
		id: 'dev',
		title: '开发工具',
		tools: toolsConfig.filter(tool => tool.category === 'dev')
	},
	{
		id: 'text',
		title: '文本工具',
		tools: toolsConfig.filter(tool => tool.category === 'text')
	}
]

// 辅助函数：根据类别获取工具列表
export const getToolsByCategory = (category: ToolCategory): ToolItem[] => {
	return toolsConfig.filter(tool => tool.category === category)
}

// 辅助函数：获取侧边栏项目
export const getSidebarItems = (category: ToolCategory) => {
	return getToolsByCategory(category).map(tool => ({
		title: tool.title,
		href: tool.path,
		icon: tool.icon
	}))
}

// 辅助函数：获取首页卡片项目
export const getHomeCardItems = (category: ToolCategory) => {
	return getToolsByCategory(category).map(tool => {
		const Icon = tool.icon;
		return {
			name: tool.title,
			description: tool.description,
			path: tool.path,
			icon: <Icon className="w-8 h-8" style={{ color: tool.color || '#1890FF' }} />
		};
	});
}
