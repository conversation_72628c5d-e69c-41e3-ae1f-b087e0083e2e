import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@renderer/components/ui/card'
import { Button } from '@renderer/components/ui/button'
import { Table2, List, Grid3X3, Upload, Trash2 } from 'lucide-react'
import { useVideoStore, VideoDisplayMode } from '@renderer/store/videoStore'
import { VideoTableView } from './VideoTableView'
import { VideoListDetailView } from './VideoListDetailView'
import { VideoGridView } from './VideoGridView'
import { toast } from 'sonner'

interface VideoMetadata {
  format: {
    filename: string;
    duration: string;
    size: string;
    bit_rate: string;
    format_name?: string;
    format_long_name?: string;
    start_time?: string;
    nb_streams?: number;
    tags?: Record<string, string>;
  };
  streams: {
    codec_type: string;
    codec_name: string;
    width?: number;
    height?: number;
    display_aspect_ratio?: string;
    r_frame_rate?: string;
    avg_frame_rate?: string;
    bit_rate?: string;
    channels?: number;
    channel_layout?: string;
    sample_rate?: string;
  }[];
}

interface MultiVideoDisplayProps {
  videoInfoMap: Map<string, VideoMetadata | null>;
  loadingMap: Map<string, boolean>;
  errorMap: Map<string, string | null>;
  onGetVideoInfo: (filePath: string) => void;
  onSelectFiles: () => void;
  onClear: () => void;
}

export function MultiVideoDisplay({
  videoInfoMap,
  loadingMap,
  errorMap,
  onGetVideoInfo,
  onSelectFiles,
  onClear
}: MultiVideoDisplayProps) {
  const { files, displayMode, setDisplayMode } = useVideoStore()
  const [isDragging, setIsDragging] = useState(false)

  // 显示模式配置
  const displayModes: Array<{
    key: VideoDisplayMode;
    label: string;
    icon: React.ReactNode;
    description: string;
  }> = [
      {
        key: 'table',
        label: '表格',
        icon: <Table2 className="h-4 w-4" />,
        description: '表格模式'
      },
      {
        key: 'list-detail',
        label: '列表详情',
        icon: <List className="h-4 w-4" />,
        description: '列表+详情模式'
      },
      {
        key: 'grid',
        label: '网格对比',
        icon: <Grid3X3 className="h-4 w-4" />,
        description: '网格对比模式'
      }
    ]

  // 处理拖放功能
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    try {
      const files = Array.from(e.dataTransfer.files);

      // 过滤视频文件
      const videoFiles = files.filter(file => {
        const videoTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/mkv', 'video/webm', 'video/flv', 'video/wmv'];
        return videoTypes.includes(file.type) ||
          /\.(mp4|avi|mov|mkv|webm|flv|wmv|m4v|3gp|ts|mts|m2ts)$/i.test(file.name);
      });

      if (videoFiles.length === 0) {
        toast.error('请拖拽视频文件');
        return;
      }

      // 获取文件路径
      const filePaths: string[] = [];
      for (const file of videoFiles) {
        try {
          const filePath = window.api.getPathForFile(file);
          if (filePath && typeof filePath === 'string') {
            filePaths.push(filePath);
          }
        } catch (error) {
          console.warn('获取文件路径时出错:', file.name, error);
        }
      }

      if (filePaths.length === 0) {
        toast.error('无法获取文件路径，请尝试使用文件选择按钮');
        return;
      }

      // 添加文件到store
      const { addFiles } = useVideoStore.getState();
      addFiles(filePaths);

      toast.success(`已添加 ${filePaths.length} 个视频文件`);

    } catch (error) {
      console.error('处理拖拽文件时出错:', error);
      toast.error('处理拖拽文件时出错，请重试');
    }
  };

  // 渲染当前显示模式的组件
  const renderDisplayMode = () => {
    const commonProps = {
      videoInfoMap,
      loadingMap,
      errorMap,
      onGetVideoInfo
    };

    switch (displayMode) {
      case 'table':
        return <VideoTableView {...commonProps} />;
      case 'list-detail':
        return <VideoListDetailView {...commonProps} />;
      case 'grid':
        return <VideoGridView {...commonProps} />;
      default:
        return <VideoTableView {...commonProps} />;
    }
  };

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <CardTitle className="text-base font-medium flex items-center justify-between">
          <div className="flex items-center">
            <span>视频信息 ({files.length} 个文件)</span>
          </div>
          <div className="flex items-center space-x-2">
            {/* 显示模式切换按钮 */}
            <div className="flex items-center space-x-1 border rounded-md p-1">
              {displayModes.map((mode) => (
                <Button
                  key={mode.key}
                  variant={displayMode === mode.key ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setDisplayMode(mode.key)}
                  className="h-7 px-2"
                  title={mode.description}
                >
                  {mode.icon}
                </Button>
              ))}
            </div>

            {/* 操作按钮 */}
            <Button
              variant="outline"
              size="sm"
              onClick={onClear}
              className="h-8"
              title="清空所有文件"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              清空
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={onSelectFiles}
              className="h-8"
            >
              <Upload className="h-4 w-4 mr-2" />
              添加文件
            </Button>
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="flex-1 min-h-0">
        <div className="h-full flex flex-col">
          {/* 拖拽区域 */}
          <div
            className={`border-2 border-dashed rounded-lg p-4 mb-4 transition-colors ${isDragging
              ? 'border-[#1890FF] bg-blue-50'
              : 'border-gray-300 hover:border-gray-400'
              }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <div className="text-center">
              <Upload className="h-8 w-8 mx-auto text-gray-400 mb-2" />
              <p className="text-sm text-gray-600">
                拖拽视频文件到此处，或点击"添加文件"按钮选择文件
              </p>
            </div>
          </div>

          {/* 显示模式内容 */}
          <div className="flex-1 min-h-0">
            {renderDisplayMode()}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
