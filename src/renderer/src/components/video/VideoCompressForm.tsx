import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Progress } from "@/components/ui/progress"

interface VideoCompressFormProps {
	settings: {
		resolution: string
		fps: number
		bitrate: number
		saveDirectory: string
		preset: string
		deleteOriginal: boolean
	}
	onSettingsChange: (settings: any) => void
	onSelectDirectory: () => void
	onCompress: () => void
	onStop?: () => void
	disabled: boolean
	isProcessing: boolean
	progress?: number
}

export function VideoCompressForm({
	settings,
	onSettingsChange,
	onSelectDirectory,
	onCompress,
	onStop,
	disabled,
	isProcessing,
	progress = 0
}: VideoCompressFormProps) {
	const resolutions = [
		{ value: '480p', label: '480p (SD)', width: 854, height: 480 },
		{ value: '720p', label: '720p (HD)', width: 1280, height: 720 },
		{ value: '1080p', label: '1080p (Full HD)', width: 1920, height: 1080 },
		{ value: '1440p', label: '1440p (2K)', width: 2560, height: 1440 },
		{ value: '2160p', label: '2160p (4K)', width: 3840, height: 2160 }
	]

	const frameRates = [
		{ value: 15, label: '15 fps (省空间)' },
		{ value: 24, label: '24 fps (电影标准)' },
		{ value: 30, label: '30 fps (视频标准)' },
		{ value: 60, label: '60 fps (高流畅度)' }
	]

	const bitrates = [
		{ value: 500, label: '500 Kbps (低质量)' },
		{ value: 1000, label: '1000 Kbps (标准质量)' },
		{ value: 2000, label: '2000 Kbps (高质量)' },
		{ value: 4000, label: '4000 Kbps (超高质量)' },
		{ value: 8000, label: '8000 Kbps (蓝光质量)' }
	]

	const presets = [
		{ value: 'ultrafast', label: '极速 (最快，质量最低)' },
		{ value: 'superfast', label: '超快 (很快，质量较低)' },
		{ value: 'veryfast', label: '很快 (快速，质量一般)' },
		{ value: 'faster', label: '较快 (较快，质量较好)' },
		{ value: 'fast', label: '快速 (快，质量好)' },
		{ value: 'medium', label: '中等 (平衡速度和质量)' },
		{ value: 'slow', label: '慢速 (慢，质量很好)' },
		{ value: 'slower', label: '较慢 (较慢，质量更好)' },
		{ value: 'veryslow', label: '很慢 (最慢，质量最高)' }
	]

	return (
		<div className="space-y-4">
			<div className="space-y-4 w-full">
				<div className="space-y-2 w-full">
					<Label htmlFor="resolution">分辨率</Label>
					<Select
						value={settings.resolution}
						onValueChange={(value) => onSettingsChange({ resolution: value })}
						disabled={isProcessing}
					>
						<SelectTrigger id="resolution">
							<SelectValue placeholder="选择分辨率" />
						</SelectTrigger>
						<SelectContent>
							{resolutions.map(res => (
								<SelectItem key={res.value} value={res.value}>
									{res.label}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>

				<div className="space-y-2 w-full">
					<Label htmlFor="fps">帧率 (FPS)</Label>
					<Select
						value={settings.fps.toString()}
						onValueChange={(value) => onSettingsChange({ fps: Number(value) })}
						disabled={isProcessing}
					>
						<SelectTrigger id="fps">
							<SelectValue placeholder="选择帧率" />
						</SelectTrigger>
						<SelectContent>
							{frameRates.map(fps => (
								<SelectItem key={fps.value} value={fps.value.toString()}>
									{fps.label}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>

				<div className="space-y-2 w-full">
					<Label htmlFor="bitrate">比特率</Label>
					<Select
						value={settings.bitrate.toString()}
						onValueChange={(value) => onSettingsChange({ bitrate: Number(value) })}
						disabled={isProcessing}
					>
						<SelectTrigger id="bitrate">
							<SelectValue placeholder="选择比特率" />
						</SelectTrigger>
						<SelectContent>
							{bitrates.map(bitrate => (
								<SelectItem key={bitrate.value} value={bitrate.value.toString()}>
									{bitrate.label}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>
			</div>

			<div className="space-y-2 w-full">
				<Label htmlFor="preset">编码预设</Label>
				<Select
					value={settings.preset}
					onValueChange={(value) => onSettingsChange({ preset: value })}
					disabled={isProcessing}
				>
					<SelectTrigger id="preset">
						<SelectValue placeholder="选择编码预设" />
					</SelectTrigger>
					<SelectContent>
						{presets.map(preset => (
							<SelectItem key={preset.value} value={preset.value}>
								{preset.label}
							</SelectItem>
						))}
					</SelectContent>
				</Select>
				<p className="text-xs text-[#8C8C8C] mt-1">预设决定了压缩速度和质量的平衡，越慢的预设压缩质量越高</p>
			</div>

			<div className="space-y-2 w-full">
				<Label htmlFor="save-directory">保存目录</Label>
				<div className="flex w-full">
					<Input
						id="save-directory"
						value={settings.saveDirectory}
						placeholder="默认与原视频相同目录"
						className="rounded-r-none"
						readOnly
						disabled={isProcessing}
					/>
					<Button
						variant="secondary"
						className="rounded-l-none"
						onClick={onSelectDirectory}
						disabled={isProcessing}
					>
						浏览
					</Button>
				</div>
			</div>

			<div className="flex items-center space-x-2 w-full">
				<Checkbox
					id="delete-original"
					checked={settings.deleteOriginal}
					onCheckedChange={(checked) => onSettingsChange({ deleteOriginal: checked })}
					disabled={isProcessing}
				/>
				<Label
					htmlFor="delete-original"
					className="text-sm font-normal cursor-pointer"
				>
					压缩完成后删除原视频
				</Label>
			</div>

			{isProcessing && (
				<div className="space-y-2 w-full">
					<Progress value={progress} />
					<p className="text-xs text-[#8C8C8C] text-right">{Math.round(progress)}%</p>
				</div>
			)}

			<div className="flex justify-end space-x-2 w-full">
				{isProcessing && onStop ? (
					<Button
						className="mt-4 w-full flex items-center justify-center gap-2 bg-red-500 hover:bg-red-600 text-white py-2 rounded-lg text-sm"
						onClick={onStop}
					>
						停止压缩
					</Button>
				) : (
					<Button
						onClick={onCompress}
						disabled={disabled}
						className="w-full"
					>
						开始压缩
					</Button>
				)}
			</div>
		</div>
	)
}
