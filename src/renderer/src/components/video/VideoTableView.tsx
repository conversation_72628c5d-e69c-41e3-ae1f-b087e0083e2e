import { useState } from 'react'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@renderer/components/ui/table'
import { Button } from '@renderer/components/ui/button'
import { <PERSON><PERSON>, DialogContent, DialogHeader, <PERSON><PERSON>Title, DialogTrigger } from '@renderer/components/ui/dialog'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@renderer/components/ui/tooltip'
import { X, Folder, RefreshCw, Loader2, AlertCircle, FileVideo, Music, Info, Eye } from 'lucide-react'
import { useVideoStore } from '@renderer/store/videoStore'
import { getFileName, formatBitrate, formatDuration, formatFileSize, formatFrameRate, formatCodecName } from '@renderer/utils/fileUtils'

interface VideoMetadata {
  format: {
    filename: string;
    duration: string;
    size: string;
    bit_rate: string;
    format_name?: string;
    format_long_name?: string;
    start_time?: string;
    nb_streams?: number;
    tags?: Record<string, string>;
  };
  streams: {
    codec_type: string;
    codec_name: string;
    width?: number;
    height?: number;
    display_aspect_ratio?: string;
    r_frame_rate?: string;
    avg_frame_rate?: string;
    bit_rate?: string;
    channels?: number;
    channel_layout?: string;
    sample_rate?: string;
  }[];
}

interface VideoTableViewProps {
  videoInfoMap: Map<string, VideoMetadata | null>;
  loadingMap: Map<string, boolean>;
  errorMap: Map<string, string | null>;
  onGetVideoInfo: (filePath: string) => void;
}

export function VideoTableView({
  videoInfoMap,
  loadingMap,
  errorMap,
  onGetVideoInfo
}: VideoTableViewProps) {
  const { files, removeFile } = useVideoStore()
  const [selectedDetailFile, setSelectedDetailFile] = useState<string | null>(null)

  const handleRemoveFile = (filePath: string) => {
    removeFile(filePath)
  }

  const handleOpenDirectory = (filePath: string) => {
    window.api.openFileDirectory(filePath)
  }

  const renderVideoInfo = (filePath: string) => {
    const videoInfo = videoInfoMap.get(filePath)
    const isLoading = loadingMap.get(filePath) || false
    const error = errorMap.get(filePath)

    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2 text-[#1890FF]" />
            <p className="text-sm text-gray-600">正在获取视频信息...</p>
          </div>
        </div>
      )
    }

    if (error) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 mx-auto mb-2 text-red-500" />
            <p className="text-sm text-red-600 mb-4">{error}</p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onGetVideoInfo(filePath)}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              重试
            </Button>
          </div>
        </div>
      )
    }

    if (!videoInfo) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <FileVideo className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm text-gray-600 mb-4">暂无视频信息</p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onGetVideoInfo(filePath)}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              获取信息
            </Button>
          </div>
        </div>
      )
    }

    // 分离视频流和音频流
    const videoStreams = videoInfo.streams.filter(stream => stream.codec_type === 'video')
    const audioStreams = videoInfo.streams.filter(stream => stream.codec_type === 'audio')

    return (
      <div className="space-y-6 max-h-full overflow-y-auto">
        {/* 文件路径显示 */}
        <div className="p-3 bg-[#F0F2F5] rounded-lg flex items-center justify-between">
          <p className="text-[#595959] truncate flex-1">{filePath}</p>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleOpenDirectory(filePath)}
            className="ml-2 h-8"
            title="打开文件所在目录"
          >
            <Folder className="h-4 w-4" />
          </Button>
        </div>

        {/* 基本信息 */}
        <div>
          <h3 className="text-sm font-medium text-[#262626] mb-3 flex items-center">
            <Info className="h-4 w-4 mr-1 text-[#1890FF]" />
            基本信息
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <p className="text-xs text-[#8C8C8C]">文件名</p>
              <p className="text-sm text-[#262626]">{getFileName(videoInfo.format.filename)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-[#8C8C8C]">时长</p>
              <p className="text-sm text-[#262626]">{formatDuration(videoInfo.format.duration)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-[#8C8C8C]">文件大小</p>
              <p className="text-sm text-[#262626]">{formatFileSize(videoInfo.format.size)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-[#8C8C8C]">总码率</p>
              <p className="text-sm text-[#262626]">{formatBitrate(videoInfo.format.bit_rate)}</p>
            </div>
          </div>
        </div>

        {/* 视频流信息 */}
        {videoStreams.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-[#262626] mb-3 flex items-center">
              <FileVideo className="h-4 w-4 mr-1 text-[#1890FF]" />
              视频流信息
            </h3>
            <div className="space-y-4">
              {videoStreams.map((stream, index) => (
                <div key={index} className="grid grid-cols-2 gap-4 p-3 bg-[#FAFAFA] rounded-lg">
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">编码格式</p>
                    <p className="text-sm text-[#262626]">{formatCodecName(stream.codec_name)}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">分辨率</p>
                    <p className="text-sm text-[#262626]">
                      {stream.width && stream.height ? `${stream.width}×${stream.height}` : '未知'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">帧率</p>
                    <p className="text-sm text-[#262626]">{formatFrameRate(stream.r_frame_rate || '')}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">码率</p>
                    <p className="text-sm text-[#262626]">{formatBitrate(stream.bit_rate || '')}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 音频流信息 */}
        {audioStreams.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-[#262626] mb-3 flex items-center">
              <Music className="h-4 w-4 mr-1 text-[#1890FF]" />
              音频流信息
            </h3>
            <div className="space-y-4">
              {audioStreams.map((stream, index) => (
                <div key={index} className="grid grid-cols-2 gap-4 p-3 bg-[#FAFAFA] rounded-lg">
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">编码格式</p>
                    <p className="text-sm text-[#262626]">{formatCodecName(stream.codec_name)}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">采样率</p>
                    <p className="text-sm text-[#262626]">
                      {stream.sample_rate ? `${stream.sample_rate} Hz` : '未知'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">声道数</p>
                    <p className="text-sm text-[#262626]">
                      {stream.channels ? `${stream.channels} 声道` : '未知'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">码率</p>
                    <p className="text-sm text-[#262626]">{formatBitrate(stream.bit_rate || '')}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    )
  }

  // 获取视频基本信息用于表格显示
  const getVideoBasicInfo = (filePath: string) => {
    const videoInfo = videoInfoMap.get(filePath)
    const isLoading = loadingMap.get(filePath) || false
    const error = errorMap.get(filePath)

    if (isLoading) {
      return {
        fileName: getFileName(filePath),
        resolution: '加载中...',
        duration: '加载中...',
        bitrate: '加载中...',
        frameRate: '加载中...',
        codec: '加载中...',
        fileSize: '加载中...',
        status: 'loading' as const
      }
    }

    if (error) {
      return {
        fileName: getFileName(filePath),
        resolution: '错误',
        duration: '错误',
        bitrate: '错误',
        frameRate: '错误',
        codec: '错误',
        fileSize: '错误',
        status: 'error' as const
      }
    }

    if (!videoInfo) {
      return {
        fileName: getFileName(filePath),
        resolution: '未知',
        duration: '未知',
        bitrate: '未知',
        frameRate: '未知',
        codec: '未知',
        fileSize: '未知',
        status: 'no-info' as const
      }
    }

    const videoStream = videoInfo.streams.find(stream => stream.codec_type === 'video')

    return {
      fileName: getFileName(videoInfo.format.filename),
      resolution: videoStream?.width && videoStream?.height
        ? `${videoStream.width}×${videoStream.height}`
        : '未知',
      duration: formatDuration(videoInfo.format.duration),
      bitrate: formatBitrate(videoInfo.format.bit_rate),
      frameRate: formatFrameRate(videoStream?.r_frame_rate || ''),
      codec: formatCodecName(videoStream?.codec_name || ''),
      fileSize: formatFileSize(videoInfo.format.size),
      status: 'success' as const
    }
  }

  if (files.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <FileVideo className="h-8 w-8 mx-auto mb-2 text-gray-400" />
          <p className="text-sm text-gray-600">暂无视频文件</p>
        </div>
      </div>
    )
  }

  return (
    <TooltipProvider>
      <div className="h-full flex flex-col">
        <div className="flex-1 overflow-auto">
          <Table className="border-collapse">
            <TableHeader>
              <TableRow className="border-b border-gray-200 bg-gray-50/50">
                <TableHead className="w-[200px] font-semibold text-gray-700 py-3">文件名</TableHead>
                <TableHead className="w-[120px] font-semibold text-gray-700 py-3 text-center">分辨率</TableHead>
                <TableHead className="w-[100px] font-semibold text-gray-700 py-3 text-center">时长</TableHead>
                <TableHead className="w-[100px] font-semibold text-gray-700 py-3 text-center">码率</TableHead>
                <TableHead className="w-[80px] font-semibold text-gray-700 py-3 text-center">帧率</TableHead>
                <TableHead className="w-[100px] font-semibold text-gray-700 py-3 text-center">编码</TableHead>
                <TableHead className="w-[100px] font-semibold text-gray-700 py-3 text-center">文件大小</TableHead>
                <TableHead className="w-[140px] font-semibold text-gray-700 py-3 text-center">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {files.map((file) => {
                const basicInfo = getVideoBasicInfo(file.path)
                const isLoading = loadingMap.get(file.path) || false
                const error = errorMap.get(file.path)

                return (
                  <TableRow key={file.path} className="border-b border-gray-100 hover:bg-gray-50/50 transition-colors">
                    <TableCell className="py-3">
                      <div className="flex items-center space-x-2">
                        {isLoading && <Loader2 className="h-4 w-4 animate-spin text-[#1890FF] flex-shrink-0" />}
                        {error && <AlertCircle className="h-4 w-4 text-red-500 flex-shrink-0" />}
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="truncate max-w-[160px] font-medium text-gray-900 cursor-default">
                              {basicInfo.fileName}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent side="top" className="max-w-xs">
                            <p className="break-all">{basicInfo.fileName}</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                    </TableCell>
                    <TableCell className="text-center py-3 text-gray-700">{basicInfo.resolution}</TableCell>
                    <TableCell className="text-center py-3 text-gray-700">{basicInfo.duration}</TableCell>
                    <TableCell className="text-center py-3 text-gray-700">{basicInfo.bitrate}</TableCell>
                    <TableCell className="text-center py-3 text-gray-700">{basicInfo.frameRate}</TableCell>
                    <TableCell className="text-center py-3 text-gray-700">{basicInfo.codec}</TableCell>
                    <TableCell className="text-center py-3 text-gray-700">{basicInfo.fileSize}</TableCell>
                    <TableCell className="py-3">
                      <div className="flex items-center justify-center space-x-1">
                        {/* 详细信息按钮 */}
                        <Dialog>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <DialogTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0 hover:bg-blue-50 hover:text-blue-600 transition-colors"
                                  onClick={() => setSelectedDetailFile(file.path)}
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                              </DialogTrigger>
                            </TooltipTrigger>
                            <TooltipContent side="top">
                              <p>查看详细信息</p>
                            </TooltipContent>
                          </Tooltip>
                          <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                            <DialogHeader>
                              <DialogTitle className="text-base">
                                {basicInfo.fileName} - 详细信息
                              </DialogTitle>
                            </DialogHeader>
                            {selectedDetailFile === file.path && renderVideoInfo(file.path)}
                          </DialogContent>
                        </Dialog>

                        {/* 打开目录按钮 */}
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 hover:bg-green-50 hover:text-green-600 transition-colors"
                              onClick={() => handleOpenDirectory(file.path)}
                            >
                              <Folder className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent side="top">
                            <p>打开文件所在目录</p>
                          </TooltipContent>
                        </Tooltip>

                        {/* 重试按钮 */}
                        {(error || basicInfo.status === 'no-info') && (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0 hover:bg-orange-50 hover:text-orange-600 transition-colors"
                                onClick={() => onGetVideoInfo(file.path)}
                              >
                                <RefreshCw className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent side="top">
                              <p>重新获取信息</p>
                            </TooltipContent>
                          </Tooltip>
                        )}

                        {/* 删除按钮 */}
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 hover:bg-red-50 hover:text-red-600 transition-colors"
                              onClick={() => handleRemoveFile(file.path)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent side="top">
                            <p>删除文件</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </div>
      </div>
    </TooltipProvider>
  )
}
