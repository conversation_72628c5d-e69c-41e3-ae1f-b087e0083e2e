import { useState, useMemo } from 'react'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@renderer/components/ui/table'
import { Button } from '@renderer/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@renderer/components/ui/dialog'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@renderer/components/ui/tooltip'
import { X, Folder, RefreshCw, Loader2, AlertCircle, FileVideo, Music, Info, Eye, ChevronUp, ChevronDown, ChevronsUpDown } from 'lucide-react'
import { useVideoStore } from '@renderer/store/videoStore'
import { getFileName, formatBitrate, formatDuration, formatFileSize, formatFrameRate, formatCodecName } from '@renderer/utils/fileUtils'

interface VideoMetadata {
  format: {
    filename: string;
    duration: string;
    size: string;
    bit_rate: string;
    format_name?: string;
    format_long_name?: string;
    start_time?: string;
    nb_streams?: number;
    tags?: Record<string, string>;
  };
  streams: {
    codec_type: string;
    codec_name: string;
    width?: number;
    height?: number;
    display_aspect_ratio?: string;
    r_frame_rate?: string;
    avg_frame_rate?: string;
    bit_rate?: string;
    channels?: number;
    channel_layout?: string;
    sample_rate?: string;
  }[];
}

// 排序字段类型
type SortField = 'fileName' | 'resolution' | 'duration' | 'bitrate' | 'frameRate' | 'codec' | 'fileSize'
type SortDirection = 'asc' | 'desc' | null

interface VideoTableViewProps {
  videoInfoMap: Map<string, VideoMetadata | null>;
  loadingMap: Map<string, boolean>;
  errorMap: Map<string, string | null>;
  onGetVideoInfo: (filePath: string) => void;
}

export function VideoTableView({
  videoInfoMap,
  loadingMap,
  errorMap,
  onGetVideoInfo
}: VideoTableViewProps) {
  const { files, removeFile } = useVideoStore()
  const [selectedDetailFile, setSelectedDetailFile] = useState<string | null>(null)
  const [sortField, setSortField] = useState<SortField | null>(null)
  const [sortDirection, setSortDirection] = useState<SortDirection>(null)

  const handleRemoveFile = (filePath: string) => {
    removeFile(filePath)
  }

  const handleOpenDirectory = (filePath: string) => {
    window.api.openFileDirectory(filePath)
  }

  // 处理排序
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      // 如果点击的是当前排序字段，切换排序方向
      if (sortDirection === 'asc') {
        setSortDirection('desc')
      } else if (sortDirection === 'desc') {
        setSortDirection(null)
        setSortField(null)
      } else {
        setSortDirection('asc')
      }
    } else {
      // 如果点击的是新字段，设置为升序
      setSortField(field)
      setSortDirection('asc')
    }
  }

  const renderVideoInfo = (filePath: string) => {
    const videoInfo = videoInfoMap.get(filePath)
    const isLoading = loadingMap.get(filePath) || false
    const error = errorMap.get(filePath)

    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2 text-[#1890FF]" />
            <p className="text-sm text-gray-600">正在获取视频信息...</p>
          </div>
        </div>
      )
    }

    if (error) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 mx-auto mb-2 text-red-500" />
            <p className="text-sm text-red-600 mb-4">{error}</p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onGetVideoInfo(filePath)}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              重试
            </Button>
          </div>
        </div>
      )
    }

    if (!videoInfo) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <FileVideo className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm text-gray-600 mb-4">暂无视频信息</p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onGetVideoInfo(filePath)}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              获取信息
            </Button>
          </div>
        </div>
      )
    }

    // 分离视频流和音频流
    const videoStreams = videoInfo.streams.filter(stream => stream.codec_type === 'video')
    const audioStreams = videoInfo.streams.filter(stream => stream.codec_type === 'audio')

    return (
      <div className="space-y-6 max-h-full overflow-y-auto">
        {/* 文件路径显示 */}
        <div className="p-3 bg-[#F0F2F5] rounded-lg flex items-center justify-between">
          <p className="text-[#595959] truncate flex-1">{filePath}</p>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleOpenDirectory(filePath)}
            className="ml-2 h-8"
            title="打开文件所在目录"
          >
            <Folder className="h-4 w-4" />
          </Button>
        </div>

        {/* 基本信息 */}
        <div>
          <h3 className="text-sm font-medium text-[#262626] mb-3 flex items-center">
            <Info className="h-4 w-4 mr-1 text-[#1890FF]" />
            基本信息
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <p className="text-xs text-[#8C8C8C]">文件名</p>
              <p className="text-sm text-[#262626]">{getFileName(videoInfo.format.filename)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-[#8C8C8C]">时长</p>
              <p className="text-sm text-[#262626]">{formatDuration(videoInfo.format.duration)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-[#8C8C8C]">文件大小</p>
              <p className="text-sm text-[#262626]">{formatFileSize(videoInfo.format.size)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-[#8C8C8C]">总码率</p>
              <p className="text-sm text-[#262626]">{formatBitrate(videoInfo.format.bit_rate)}</p>
            </div>
          </div>
        </div>

        {/* 视频流信息 */}
        {videoStreams.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-[#262626] mb-3 flex items-center">
              <FileVideo className="h-4 w-4 mr-1 text-[#1890FF]" />
              视频流信息
            </h3>
            <div className="space-y-4">
              {videoStreams.map((stream, index) => (
                <div key={index} className="grid grid-cols-2 gap-4 p-3 bg-[#FAFAFA] rounded-lg">
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">编码格式</p>
                    <p className="text-sm text-[#262626]">{formatCodecName(stream.codec_name)}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">分辨率</p>
                    <p className="text-sm text-[#262626]">
                      {stream.width && stream.height ? `${stream.width}×${stream.height}` : '未知'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">帧率</p>
                    <p className="text-sm text-[#262626]">{formatFrameRate(stream.r_frame_rate || '')}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">码率</p>
                    <p className="text-sm text-[#262626]">{formatBitrate(stream.bit_rate || '')}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 音频流信息 */}
        {audioStreams.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-[#262626] mb-3 flex items-center">
              <Music className="h-4 w-4 mr-1 text-[#1890FF]" />
              音频流信息
            </h3>
            <div className="space-y-4">
              {audioStreams.map((stream, index) => (
                <div key={index} className="grid grid-cols-2 gap-4 p-3 bg-[#FAFAFA] rounded-lg">
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">编码格式</p>
                    <p className="text-sm text-[#262626]">{formatCodecName(stream.codec_name)}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">采样率</p>
                    <p className="text-sm text-[#262626]">
                      {stream.sample_rate ? `${stream.sample_rate} Hz` : '未知'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">声道数</p>
                    <p className="text-sm text-[#262626]">
                      {stream.channels ? `${stream.channels} 声道` : '未知'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">码率</p>
                    <p className="text-sm text-[#262626]">{formatBitrate(stream.bit_rate || '')}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    )
  }

  // 解析数值用于排序
  const parseResolution = (resolution: string): number => {
    if (resolution === '未知' || resolution === '加载中...' || resolution === '错误') return 0
    const match = resolution.match(/(\d+)×(\d+)/)
    if (match) {
      return parseInt(match[1]) * parseInt(match[2]) // 返回像素总数
    }
    return 0
  }

  const parseDuration = (duration: string): number => {
    if (duration === '未知' || duration === '加载中...' || duration === '错误') return 0
    const parts = duration.split(':')
    if (parts.length === 3) {
      const hours = parseInt(parts[0]) || 0
      const minutes = parseInt(parts[1]) || 0
      const seconds = parseInt(parts[2]) || 0
      return hours * 3600 + minutes * 60 + seconds
    }
    return 0
  }

  const parseBitrate = (bitrate: string): number => {
    if (bitrate === '未知' || bitrate === '加载中...' || bitrate === '错误') return 0
    const match = bitrate.match(/([\d.]+)\s*(Mbps|Kbps|bps)/i)
    if (match) {
      const value = parseFloat(match[1])
      const unit = match[2].toLowerCase()
      if (unit === 'mbps') return value * 1000000
      if (unit === 'kbps') return value * 1000
      return value
    }
    return 0
  }

  const parseFrameRate = (frameRate: string): number => {
    if (frameRate === '未知' || frameRate === '加载中...' || frameRate === '错误') return 0
    const match = frameRate.match(/([\d.]+)/)
    return match ? parseFloat(match[1]) : 0
  }

  const parseFileSize = (fileSize: string): number => {
    if (fileSize === '未知' || fileSize === '加载中...' || fileSize === '错误') return 0
    const match = fileSize.match(/([\d.]+)\s*(GB|MB|KB|B)/i)
    if (match) {
      const value = parseFloat(match[1])
      const unit = match[2].toLowerCase()
      if (unit === 'gb') return value * 1024 * 1024 * 1024
      if (unit === 'mb') return value * 1024 * 1024
      if (unit === 'kb') return value * 1024
      return value
    }
    return 0
  }

  // 获取视频基本信息用于表格显示
  const getVideoBasicInfo = (filePath: string) => {
    const videoInfo = videoInfoMap.get(filePath)
    const isLoading = loadingMap.get(filePath) || false
    const error = errorMap.get(filePath)

    if (isLoading) {
      return {
        fileName: getFileName(filePath),
        resolution: '加载中...',
        duration: '加载中...',
        bitrate: '加载中...',
        frameRate: '加载中...',
        codec: '加载中...',
        fileSize: '加载中...',
        status: 'loading' as const
      }
    }

    if (error) {
      return {
        fileName: getFileName(filePath),
        resolution: '错误',
        duration: '错误',
        bitrate: '错误',
        frameRate: '错误',
        codec: '错误',
        fileSize: '错误',
        status: 'error' as const
      }
    }

    if (!videoInfo) {
      return {
        fileName: getFileName(filePath),
        resolution: '未知',
        duration: '未知',
        bitrate: '未知',
        frameRate: '未知',
        codec: '未知',
        fileSize: '未知',
        status: 'no-info' as const
      }
    }

    const videoStream = videoInfo.streams.find(stream => stream.codec_type === 'video')

    return {
      fileName: getFileName(videoInfo.format.filename),
      resolution: videoStream?.width && videoStream?.height
        ? `${videoStream.width}×${videoStream.height}`
        : '未知',
      duration: formatDuration(videoInfo.format.duration),
      bitrate: formatBitrate(videoInfo.format.bit_rate),
      frameRate: formatFrameRate(videoStream?.r_frame_rate || ''),
      codec: formatCodecName(videoStream?.codec_name || ''),
      fileSize: formatFileSize(videoInfo.format.size),
      status: 'success' as const
    }
  }

  // 排序后的文件列表
  const sortedFiles = useMemo(() => {
    if (!sortField || !sortDirection) {
      return files
    }

    return [...files].sort((a, b) => {
      const aInfo = getVideoBasicInfo(a.path)
      const bInfo = getVideoBasicInfo(b.path)

      let aValue: string | number
      let bValue: string | number

      switch (sortField) {
        case 'fileName':
          aValue = aInfo.fileName.toLowerCase()
          bValue = bInfo.fileName.toLowerCase()
          break
        case 'resolution':
          aValue = parseResolution(aInfo.resolution)
          bValue = parseResolution(bInfo.resolution)
          break
        case 'duration':
          aValue = parseDuration(aInfo.duration)
          bValue = parseDuration(bInfo.duration)
          break
        case 'bitrate':
          aValue = parseBitrate(aInfo.bitrate)
          bValue = parseBitrate(bInfo.bitrate)
          break
        case 'frameRate':
          aValue = parseFrameRate(aInfo.frameRate)
          bValue = parseFrameRate(bInfo.frameRate)
          break
        case 'codec':
          aValue = aInfo.codec.toLowerCase()
          bValue = bInfo.codec.toLowerCase()
          break
        case 'fileSize':
          aValue = parseFileSize(aInfo.fileSize)
          bValue = parseFileSize(bInfo.fileSize)
          break
        default:
          return 0
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        const result = aValue.localeCompare(bValue)
        return sortDirection === 'asc' ? result : -result
      } else {
        const result = (aValue as number) - (bValue as number)
        return sortDirection === 'asc' ? result : -result
      }
    })
  }, [files, sortField, sortDirection, videoInfoMap, loadingMap, errorMap])

  // 渲染排序图标
  const renderSortIcon = (field: SortField) => {
    if (sortField !== field) {
      return <ChevronsUpDown className="h-3 w-3 text-gray-400" />
    }
    if (sortDirection === 'asc') {
      return <ChevronUp className="h-3 w-3 text-blue-600" />
    }
    if (sortDirection === 'desc') {
      return <ChevronDown className="h-3 w-3 text-blue-600" />
    }
    return <ChevronsUpDown className="h-3 w-3 text-gray-400" />
  }

  if (files.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <FileVideo className="h-8 w-8 mx-auto mb-2 text-gray-400" />
          <p className="text-sm text-gray-600">暂无视频文件</p>
        </div>
      </div>
    )
  }

  return (
    <TooltipProvider>
      <div className="h-full flex flex-col">
        <div className="flex-1 overflow-auto">
          <Table className="border-collapse">
            <TableHeader>
              <TableRow className="border-b border-gray-200 bg-gray-50/50">
                <TableHead className="w-[200px] py-3">
                  <Button
                    variant="ghost"
                    className="h-auto p-0 font-semibold text-gray-700 hover:text-blue-600 flex items-center space-x-1"
                    onClick={() => handleSort('fileName')}
                  >
                    <span>文件名</span>
                    {renderSortIcon('fileName')}
                  </Button>
                </TableHead>
                <TableHead className="w-[120px] py-3 text-center">
                  <Button
                    variant="ghost"
                    className="h-auto p-0 font-semibold text-gray-700 hover:text-blue-600 flex items-center space-x-1 mx-auto"
                    onClick={() => handleSort('resolution')}
                  >
                    <span>分辨率</span>
                    {renderSortIcon('resolution')}
                  </Button>
                </TableHead>
                <TableHead className="w-[100px] py-3 text-center">
                  <Button
                    variant="ghost"
                    className="h-auto p-0 font-semibold text-gray-700 hover:text-blue-600 flex items-center space-x-1 mx-auto"
                    onClick={() => handleSort('duration')}
                  >
                    <span>时长</span>
                    {renderSortIcon('duration')}
                  </Button>
                </TableHead>
                <TableHead className="w-[100px] py-3 text-center">
                  <Button
                    variant="ghost"
                    className="h-auto p-0 font-semibold text-gray-700 hover:text-blue-600 flex items-center space-x-1 mx-auto"
                    onClick={() => handleSort('bitrate')}
                  >
                    <span>码率</span>
                    {renderSortIcon('bitrate')}
                  </Button>
                </TableHead>
                <TableHead className="w-[80px] py-3 text-center">
                  <Button
                    variant="ghost"
                    className="h-auto p-0 font-semibold text-gray-700 hover:text-blue-600 flex items-center space-x-1 mx-auto"
                    onClick={() => handleSort('frameRate')}
                  >
                    <span>帧率</span>
                    {renderSortIcon('frameRate')}
                  </Button>
                </TableHead>
                <TableHead className="w-[100px] py-3 text-center">
                  <Button
                    variant="ghost"
                    className="h-auto p-0 font-semibold text-gray-700 hover:text-blue-600 flex items-center space-x-1 mx-auto"
                    onClick={() => handleSort('codec')}
                  >
                    <span>编码</span>
                    {renderSortIcon('codec')}
                  </Button>
                </TableHead>
                <TableHead className="w-[100px] py-3 text-center">
                  <Button
                    variant="ghost"
                    className="h-auto p-0 font-semibold text-gray-700 hover:text-blue-600 flex items-center space-x-1 mx-auto"
                    onClick={() => handleSort('fileSize')}
                  >
                    <span>文件大小</span>
                    {renderSortIcon('fileSize')}
                  </Button>
                </TableHead>
                <TableHead className="w-[140px] font-semibold text-gray-700 py-3 text-center">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedFiles.map((file) => {
                const basicInfo = getVideoBasicInfo(file.path)
                const isLoading = loadingMap.get(file.path) || false
                const error = errorMap.get(file.path)

                return (
                  <TableRow key={file.path} className="border-b border-gray-100 hover:bg-gray-50/50 transition-colors">
                    <TableCell className="py-3">
                      <div className="flex items-center space-x-2">
                        {isLoading && <Loader2 className="h-4 w-4 animate-spin text-[#1890FF] flex-shrink-0" />}
                        {error && <AlertCircle className="h-4 w-4 text-red-500 flex-shrink-0" />}
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="truncate max-w-[160px] font-medium text-gray-900 cursor-default">
                              {basicInfo.fileName}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent side="top" className="max-w-xs">
                            <p className="break-all">{basicInfo.fileName}</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                    </TableCell>
                    <TableCell className="text-center py-3 text-gray-700">{basicInfo.resolution}</TableCell>
                    <TableCell className="text-center py-3 text-gray-700">{basicInfo.duration}</TableCell>
                    <TableCell className="text-center py-3 text-gray-700">{basicInfo.bitrate}</TableCell>
                    <TableCell className="text-center py-3 text-gray-700">{basicInfo.frameRate}</TableCell>
                    <TableCell className="text-center py-3 text-gray-700">{basicInfo.codec}</TableCell>
                    <TableCell className="text-center py-3 text-gray-700">{basicInfo.fileSize}</TableCell>
                    <TableCell className="py-3">
                      <div className="flex items-center justify-center space-x-1">
                        {/* 详细信息按钮 */}
                        <Dialog>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <DialogTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0 hover:bg-blue-50 hover:text-blue-600 transition-colors"
                                  onClick={() => setSelectedDetailFile(file.path)}
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                              </DialogTrigger>
                            </TooltipTrigger>
                            <TooltipContent side="top">
                              <p>查看详细信息</p>
                            </TooltipContent>
                          </Tooltip>
                          <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                            <DialogHeader>
                              <DialogTitle className="text-base">
                                {basicInfo.fileName} - 详细信息
                              </DialogTitle>
                            </DialogHeader>
                            {selectedDetailFile === file.path && renderVideoInfo(file.path)}
                          </DialogContent>
                        </Dialog>

                        {/* 打开目录按钮 */}
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 hover:bg-green-50 hover:text-green-600 transition-colors"
                              onClick={() => handleOpenDirectory(file.path)}
                            >
                              <Folder className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent side="top">
                            <p>打开文件所在目录</p>
                          </TooltipContent>
                        </Tooltip>

                        {/* 重试按钮 */}
                        {(error || basicInfo.status === 'no-info') && (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0 hover:bg-orange-50 hover:text-orange-600 transition-colors"
                                onClick={() => onGetVideoInfo(file.path)}
                              >
                                <RefreshCw className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent side="top">
                              <p>重新获取信息</p>
                            </TooltipContent>
                          </Tooltip>
                        )}

                        {/* 移除按钮 */}
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 hover:bg-red-50 hover:text-red-600 transition-colors"
                              onClick={() => handleRemoveFile(file.path)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent side="top">
                            <p>从列表中移除</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </div>
      </div>
    </TooltipProvider>
  )
}
