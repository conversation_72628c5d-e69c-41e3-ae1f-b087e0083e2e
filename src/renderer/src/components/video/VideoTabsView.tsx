import { useState } from 'react'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@renderer/components/ui/table'
import { Button } from '@renderer/components/ui/button'
import { <PERSON><PERSON>, DialogContent, DialogHeader, <PERSON><PERSON>Title, DialogTrigger } from '@renderer/components/ui/dialog'
import { X, Folder, RefreshCw, Loader2, AlertCircle, FileVideo, Music, Info, Eye } from 'lucide-react'
import { useVideoStore } from '@renderer/store/videoStore'
import { getFileName, formatBitrate, formatDuration, formatFileSize, formatFrameRate, formatCodecName } from '@renderer/utils/fileUtils'

interface VideoMetadata {
  format: {
    filename: string;
    duration: string;
    size: string;
    bit_rate: string;
    format_name?: string;
    format_long_name?: string;
    start_time?: string;
    nb_streams?: number;
    tags?: Record<string, string>;
  };
  streams: {
    codec_type: string;
    codec_name: string;
    width?: number;
    height?: number;
    display_aspect_ratio?: string;
    r_frame_rate?: string;
    avg_frame_rate?: string;
    bit_rate?: string;
    channels?: number;
    channel_layout?: string;
    sample_rate?: string;
  }[];
}

interface VideoTabsViewProps {
  videoInfoMap: Map<string, VideoMetadata | null>;
  loadingMap: Map<string, boolean>;
  errorMap: Map<string, string | null>;
  onGetVideoInfo: (filePath: string) => void;
}

export function VideoTabsView({
  videoInfoMap,
  loadingMap,
  errorMap,
  onGetVideoInfo
}: VideoTabsViewProps) {
  const { files, removeFile } = useVideoStore()
  const [selectedDetailFile, setSelectedDetailFile] = useState<string | null>(null)

  const handleRemoveFile = (filePath: string) => {
    removeFile(filePath)
  }

  const handleOpenDirectory = (filePath: string) => {
    window.api.openFileDirectory(filePath)
  }

  const renderVideoInfo = (filePath: string) => {
    const videoInfo = videoInfoMap.get(filePath)
    const isLoading = loadingMap.get(filePath) || false
    const error = errorMap.get(filePath)

    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2 text-[#1890FF]" />
            <p className="text-sm text-gray-600">正在获取视频信息...</p>
          </div>
        </div>
      )
    }

    if (error) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 mx-auto mb-2 text-red-500" />
            <p className="text-sm text-red-600 mb-4">{error}</p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onGetVideoInfo(filePath)}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              重试
            </Button>
          </div>
        </div>
      )
    }

    if (!videoInfo) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <FileVideo className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm text-gray-600 mb-4">暂无视频信息</p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onGetVideoInfo(filePath)}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              获取信息
            </Button>
          </div>
        </div>
      )
    }

    // 分离视频流和音频流
    const videoStreams = videoInfo.streams.filter(stream => stream.codec_type === 'video')
    const audioStreams = videoInfo.streams.filter(stream => stream.codec_type === 'audio')

    return (
      <div className="space-y-6 max-h-full overflow-y-auto">
        {/* 文件路径显示 */}
        <div className="p-3 bg-[#F0F2F5] rounded-lg flex items-center justify-between">
          <p className="text-[#595959] truncate flex-1">{filePath}</p>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleOpenDirectory(filePath)}
            className="ml-2 h-8"
            title="打开文件所在目录"
          >
            <Folder className="h-4 w-4" />
          </Button>
        </div>

        {/* 基本信息 */}
        <div>
          <h3 className="text-sm font-medium text-[#262626] mb-3 flex items-center">
            <Info className="h-4 w-4 mr-1 text-[#1890FF]" />
            基本信息
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <p className="text-xs text-[#8C8C8C]">文件名</p>
              <p className="text-sm text-[#262626]">{getFileName(videoInfo.format.filename)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-[#8C8C8C]">时长</p>
              <p className="text-sm text-[#262626]">{formatDuration(videoInfo.format.duration)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-[#8C8C8C]">文件大小</p>
              <p className="text-sm text-[#262626]">{formatFileSize(videoInfo.format.size)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-[#8C8C8C]">总码率</p>
              <p className="text-sm text-[#262626]">{formatBitrate(videoInfo.format.bit_rate)}</p>
            </div>
          </div>
        </div>

        {/* 视频流信息 */}
        {videoStreams.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-[#262626] mb-3 flex items-center">
              <FileVideo className="h-4 w-4 mr-1 text-[#1890FF]" />
              视频流信息
            </h3>
            <div className="space-y-4">
              {videoStreams.map((stream, index) => (
                <div key={index} className="grid grid-cols-2 gap-4 p-3 bg-[#FAFAFA] rounded-lg">
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">编码格式</p>
                    <p className="text-sm text-[#262626]">{formatCodecName(stream.codec_name)}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">分辨率</p>
                    <p className="text-sm text-[#262626]">
                      {stream.width && stream.height ? `${stream.width}×${stream.height}` : '未知'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">帧率</p>
                    <p className="text-sm text-[#262626]">{formatFrameRate(stream.r_frame_rate || '')}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">码率</p>
                    <p className="text-sm text-[#262626]">{formatBitrate(stream.bit_rate || '')}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 音频流信息 */}
        {audioStreams.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-[#262626] mb-3 flex items-center">
              <Music className="h-4 w-4 mr-1 text-[#1890FF]" />
              音频流信息
            </h3>
            <div className="space-y-4">
              {audioStreams.map((stream, index) => (
                <div key={index} className="grid grid-cols-2 gap-4 p-3 bg-[#FAFAFA] rounded-lg">
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">编码格式</p>
                    <p className="text-sm text-[#262626]">{formatCodecName(stream.codec_name)}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">采样率</p>
                    <p className="text-sm text-[#262626]">
                      {stream.sample_rate ? `${stream.sample_rate} Hz` : '未知'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">声道数</p>
                    <p className="text-sm text-[#262626]">
                      {stream.channels ? `${stream.channels} 声道` : '未知'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">码率</p>
                    <p className="text-sm text-[#262626]">{formatBitrate(stream.bit_rate || '')}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    )
  }

  // 获取视频基本信息用于表格显示
  const getVideoBasicInfo = (filePath: string) => {
    const videoInfo = videoInfoMap.get(filePath)
    const isLoading = loadingMap.get(filePath) || false
    const error = errorMap.get(filePath)

    if (isLoading) {
      return {
        fileName: getFileName(filePath),
        resolution: '加载中...',
        duration: '加载中...',
        bitrate: '加载中...',
        frameRate: '加载中...',
        codec: '加载中...',
        fileSize: '加载中...',
        status: 'loading' as const
      }
    }

    if (error) {
      return {
        fileName: getFileName(filePath),
        resolution: '错误',
        duration: '错误',
        bitrate: '错误',
        frameRate: '错误',
        codec: '错误',
        fileSize: '错误',
        status: 'error' as const
      }
    }

    if (!videoInfo) {
      return {
        fileName: getFileName(filePath),
        resolution: '未知',
        duration: '未知',
        bitrate: '未知',
        frameRate: '未知',
        codec: '未知',
        fileSize: '未知',
        status: 'no-info' as const
      }
    }

    const videoStream = videoInfo.streams.find(stream => stream.codec_type === 'video')

    return {
      fileName: getFileName(videoInfo.format.filename),
      resolution: videoStream?.width && videoStream?.height
        ? `${videoStream.width}×${videoStream.height}`
        : '未知',
      duration: formatDuration(videoInfo.format.duration),
      bitrate: formatBitrate(videoInfo.format.bit_rate),
      frameRate: formatFrameRate(videoStream?.r_frame_rate || ''),
      codec: formatCodecName(videoStream?.codec_name || ''),
      fileSize: formatFileSize(videoInfo.format.size),
      status: 'success' as const
    }
  }

  if (files.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <FileVideo className="h-8 w-8 mx-auto mb-2 text-gray-400" />
          <p className="text-sm text-gray-600">暂无视频文件</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      <div className="flex-1 overflow-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[200px]">文件名</TableHead>
              <TableHead className="w-[120px]">分辨率</TableHead>
              <TableHead className="w-[100px]">时长</TableHead>
              <TableHead className="w-[100px]">码率</TableHead>
              <TableHead className="w-[80px]">帧率</TableHead>
              <TableHead className="w-[100px]">编码</TableHead>
              <TableHead className="w-[100px]">文件大小</TableHead>
              <TableHead className="w-[120px]">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {files.map((file) => {
              const basicInfo = getVideoBasicInfo(file.path)
              const isLoading = loadingMap.get(file.path) || false
              const error = errorMap.get(file.path)

              return (
                <TableRow key={file.path}>
                  <TableCell className="font-medium">
                    <div className="flex items-center space-x-2">
                      {isLoading && <Loader2 className="h-4 w-4 animate-spin text-[#1890FF]" />}
                      {error && <AlertCircle className="h-4 w-4 text-red-500" />}
                      <span className="truncate" title={basicInfo.fileName}>
                        {basicInfo.fileName}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>{basicInfo.resolution}</TableCell>
                  <TableCell>{basicInfo.duration}</TableCell>
                  <TableCell>{basicInfo.bitrate}</TableCell>
                  <TableCell>{basicInfo.frameRate}</TableCell>
                  <TableCell>{basicInfo.codec}</TableCell>
                  <TableCell>{basicInfo.fileSize}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      {/* 详细信息按钮 */}
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-7 w-7 p-0"
                            onClick={() => setSelectedDetailFile(file.path)}
                            title="查看详细信息"
                          >
                            <Eye className="h-3 w-3" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                          <DialogHeader>
                            <DialogTitle className="text-base">
                              {basicInfo.fileName} - 详细信息
                            </DialogTitle>
                          </DialogHeader>
                          {selectedDetailFile === file.path && renderVideoInfo(file.path)}
                        </DialogContent>
                      </Dialog>

                      {/* 打开目录按钮 */}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-7 w-7 p-0"
                        onClick={() => handleOpenDirectory(file.path)}
                        title="打开文件所在目录"
                      >
                        <Folder className="h-3 w-3" />
                      </Button>

                      {/* 重试按钮 */}
                      {(error || basicInfo.status === 'no-info') && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-7 w-7 p-0"
                          onClick={() => onGetVideoInfo(file.path)}
                          title="重新获取信息"
                        >
                          <RefreshCw className="h-3 w-3" />
                        </Button>
                      )}

                      {/* 删除按钮 */}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-7 w-7 p-0 hover:bg-red-100"
                        onClick={() => handleRemoveFile(file.path)}
                        title="删除文件"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
