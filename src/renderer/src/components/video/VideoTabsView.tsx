import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@renderer/components/ui/tabs'
import { Button } from '@renderer/components/ui/button'
import { X, Folder, RefreshCw, Loader2, AlertCircle, FileVideo, Music, Info } from 'lucide-react'
import { useVideoStore } from '@renderer/store/videoStore'
import { getFileName, formatBitrate, formatDuration, formatFileSize, formatFrameRate, formatCodecName } from '@renderer/utils/fileUtils'

interface VideoMetadata {
  format: {
    filename: string;
    duration: string;
    size: string;
    bit_rate: string;
    format_name?: string;
    format_long_name?: string;
    start_time?: string;
    nb_streams?: number;
    tags?: Record<string, string>;
  };
  streams: {
    codec_type: string;
    codec_name: string;
    width?: number;
    height?: number;
    display_aspect_ratio?: string;
    r_frame_rate?: string;
    avg_frame_rate?: string;
    bit_rate?: string;
    channels?: number;
    channel_layout?: string;
    sample_rate?: string;
  }[];
}

interface VideoTabsViewProps {
  videoInfoMap: Map<string, VideoMetadata | null>;
  loadingMap: Map<string, boolean>;
  errorMap: Map<string, string | null>;
  onGetVideoInfo: (filePath: string) => void;
}

export function VideoTabsView({
  videoInfoMap,
  loadingMap,
  errorMap,
  onGetVideoInfo
}: VideoTabsViewProps) {
  const { files, removeFile, currentFile, setCurrentFile } = useVideoStore()
  const [activeTab, setActiveTab] = useState(files[0]?.path || '')

  // 当activeTab不在文件列表中时，重置为第一个文件
  if (activeTab && !files.some(f => f.path === activeTab) && files.length > 0) {
    setActiveTab(files[0].path)
  }

  const handleTabChange = (filePath: string) => {
    setActiveTab(filePath)
    setCurrentFile(filePath)
  }

  const handleRemoveTab = (filePath: string, e: React.MouseEvent) => {
    e.stopPropagation()
    removeFile(filePath)

    // 如果删除的是当前活跃标签，切换到其他标签
    if (activeTab === filePath && files.length > 1) {
      const remainingFiles = files.filter(f => f.path !== filePath)
      if (remainingFiles.length > 0) {
        setActiveTab(remainingFiles[0].path)
        setCurrentFile(remainingFiles[0].path)
      }
    }
  }

  const handleOpenDirectory = (filePath: string) => {
    window.api.openFileDirectory(filePath)
  }

  const renderVideoInfo = (filePath: string) => {
    const videoInfo = videoInfoMap.get(filePath)
    const isLoading = loadingMap.get(filePath) || false
    const error = errorMap.get(filePath)

    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2 text-[#1890FF]" />
            <p className="text-sm text-gray-600">正在获取视频信息...</p>
          </div>
        </div>
      )
    }

    if (error) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 mx-auto mb-2 text-red-500" />
            <p className="text-sm text-red-600 mb-4">{error}</p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onGetVideoInfo(filePath)}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              重试
            </Button>
          </div>
        </div>
      )
    }

    if (!videoInfo) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <FileVideo className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm text-gray-600 mb-4">暂无视频信息</p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onGetVideoInfo(filePath)}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              获取信息
            </Button>
          </div>
        </div>
      )
    }

    // 分离视频流和音频流
    const videoStreams = videoInfo.streams.filter(stream => stream.codec_type === 'video')
    const audioStreams = videoInfo.streams.filter(stream => stream.codec_type === 'audio')

    return (
      <div className="space-y-6 max-h-full overflow-y-auto">
        {/* 文件路径显示 */}
        <div className="p-3 bg-[#F0F2F5] rounded-lg flex items-center justify-between">
          <p className="text-[#595959] truncate flex-1">{filePath}</p>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleOpenDirectory(filePath)}
            className="ml-2 h-8"
            title="打开文件所在目录"
          >
            <Folder className="h-4 w-4" />
          </Button>
        </div>

        {/* 基本信息 */}
        <div>
          <h3 className="text-sm font-medium text-[#262626] mb-3 flex items-center">
            <Info className="h-4 w-4 mr-1 text-[#1890FF]" />
            基本信息
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <p className="text-xs text-[#8C8C8C]">文件名</p>
              <p className="text-sm text-[#262626]">{getFileName(videoInfo.format.filename)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-[#8C8C8C]">时长</p>
              <p className="text-sm text-[#262626]">{formatDuration(videoInfo.format.duration)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-[#8C8C8C]">文件大小</p>
              <p className="text-sm text-[#262626]">{formatFileSize(videoInfo.format.size)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-[#8C8C8C]">总码率</p>
              <p className="text-sm text-[#262626]">{formatBitrate(videoInfo.format.bit_rate)}</p>
            </div>
          </div>
        </div>

        {/* 视频流信息 */}
        {videoStreams.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-[#262626] mb-3 flex items-center">
              <FileVideo className="h-4 w-4 mr-1 text-[#1890FF]" />
              视频流信息
            </h3>
            <div className="space-y-4">
              {videoStreams.map((stream, index) => (
                <div key={index} className="grid grid-cols-2 gap-4 p-3 bg-[#FAFAFA] rounded-lg">
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">编码格式</p>
                    <p className="text-sm text-[#262626]">{formatCodecName(stream.codec_name)}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">分辨率</p>
                    <p className="text-sm text-[#262626]">
                      {stream.width && stream.height ? `${stream.width}×${stream.height}` : '未知'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">帧率</p>
                    <p className="text-sm text-[#262626]">{formatFrameRate(stream.r_frame_rate || '')}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">码率</p>
                    <p className="text-sm text-[#262626]">{formatBitrate(stream.bit_rate || '')}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 音频流信息 */}
        {audioStreams.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-[#262626] mb-3 flex items-center">
              <Music className="h-4 w-4 mr-1 text-[#1890FF]" />
              音频流信息
            </h3>
            <div className="space-y-4">
              {audioStreams.map((stream, index) => (
                <div key={index} className="grid grid-cols-2 gap-4 p-3 bg-[#FAFAFA] rounded-lg">
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">编码格式</p>
                    <p className="text-sm text-[#262626]">{formatCodecName(stream.codec_name)}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">采样率</p>
                    <p className="text-sm text-[#262626]">
                      {stream.sample_rate ? `${stream.sample_rate} Hz` : '未知'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">声道数</p>
                    <p className="text-sm text-[#262626]">
                      {stream.channels ? `${stream.channels} 声道` : '未知'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">码率</p>
                    <p className="text-sm text-[#262626]">{formatBitrate(stream.bit_rate || '')}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    )
  }

  if (files.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <FileVideo className="h-8 w-8 mx-auto mb-2 text-gray-400" />
          <p className="text-sm text-gray-600">暂无视频文件</p>
        </div>
      </div>
    )
  }

  return (
    <Tabs value={activeTab} onValueChange={handleTabChange} className="h-full flex flex-col">
      <TabsList className="grid w-full grid-cols-auto gap-1 h-auto p-1 bg-gray-100">
        {files.map((file) => (
          <TabsTrigger
            key={file.path}
            value={file.path}
            className="relative group px-3 py-2 text-sm data-[state=active]:bg-white"
          >
            <span className="truncate max-w-32" title={getFileName(file.path)}>
              {getFileName(file.path)}
            </span>
            <Button
              variant="ghost"
              size="sm"
              className="ml-2 h-4 w-4 p-0 opacity-0 group-hover:opacity-100 hover:bg-red-100"
              onClick={(e) => handleRemoveTab(file.path, e)}
            >
              <X className="h-3 w-3" />
            </Button>
          </TabsTrigger>
        ))}
      </TabsList>

      <div className="flex-1 min-h-0 mt-4">
        {files.map((file) => (
          <TabsContent key={file.path} value={file.path} className="h-full mt-0">
            {renderVideoInfo(file.path)}
          </TabsContent>
        ))}
      </div>
    </Tabs>
  )
}
