import { useState } from 'react'
import { But<PERSON> } from '@renderer/components/ui/button'
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@renderer/components/ui/tooltip'
import { X, Folder, RefreshCw, Loader2, AlertCircle, FileVideo, Music, Info } from 'lucide-react'
import { useVideoStore } from '@renderer/store/videoStore'
import { getFileName, formatBitrate, formatDuration, formatFileSize, formatFrameRate, formatCodecName } from '@renderer/utils/fileUtils'
import { cn } from '@renderer/lib/utils'

interface VideoMetadata {
  format: {
    filename: string;
    duration: string;
    size: string;
    bit_rate: string;
    format_name?: string;
    format_long_name?: string;
    start_time?: string;
    nb_streams?: number;
    tags?: Record<string, string>;
  };
  streams: {
    codec_type: string;
    codec_name: string;
    width?: number;
    height?: number;
    display_aspect_ratio?: string;
    r_frame_rate?: string;
    avg_frame_rate?: string;
    bit_rate?: string;
    channels?: number;
    channel_layout?: string;
    sample_rate?: string;
  }[];
}

interface VideoListDetailViewProps {
  videoInfoMap: Map<string, VideoMetadata | null>;
  loadingMap: Map<string, boolean>;
  errorMap: Map<string, string | null>;
  onGetVideoInfo: (filePath: string) => void;
}

export function VideoListDetailView({
  videoInfoMap,
  loadingMap,
  errorMap,
  onGetVideoInfo
}: VideoListDetailViewProps) {
  const { files, removeFile, currentFile, setCurrentFile } = useVideoStore()
  const [selectedFile, setSelectedFile] = useState(files[0]?.path || '')

  // 当selectedFile不在文件列表中时，重置为第一个文件
  if (selectedFile && !files.some(f => f.path === selectedFile) && files.length > 0) {
    setSelectedFile(files[0].path)
  }

  const handleSelectFile = (filePath: string) => {
    setSelectedFile(filePath)
    setCurrentFile(filePath)
  }

  const handleRemoveFile = (filePath: string) => {
    removeFile(filePath)

    // 如果删除的是当前选中文件，切换到其他文件
    if (selectedFile === filePath && files.length > 1) {
      const remainingFiles = files.filter(f => f.path !== filePath)
      if (remainingFiles.length > 0) {
        setSelectedFile(remainingFiles[0].path)
        setCurrentFile(remainingFiles[0].path)
      }
    }
  }

  const handleOpenDirectory = (filePath: string) => {
    window.api.openFileDirectory(filePath)
  }

  const renderVideoInfo = (filePath: string) => {
    const videoInfo = videoInfoMap.get(filePath)
    const isLoading = loadingMap.get(filePath) || false
    const error = errorMap.get(filePath)

    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2 text-[#1890FF]" />
            <p className="text-sm text-gray-600">正在获取视频信息...</p>
          </div>
        </div>
      )
    }

    if (error) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 mx-auto mb-2 text-red-500" />
            <p className="text-sm text-red-600 mb-4">{error}</p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onGetVideoInfo(filePath)}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              重试
            </Button>
          </div>
        </div>
      )
    }

    if (!videoInfo) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <FileVideo className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm text-gray-600 mb-4">暂无视频信息</p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onGetVideoInfo(filePath)}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              获取信息
            </Button>
          </div>
        </div>
      )
    }

    // 分离视频流和音频流
    const videoStreams = videoInfo.streams.filter(stream => stream.codec_type === 'video')
    const audioStreams = videoInfo.streams.filter(stream => stream.codec_type === 'audio')

    return (
      <div className="space-y-6 max-h-full overflow-y-auto">
        {/* 文件路径显示 */}
        <div className="p-3 bg-[#F0F2F5] rounded-lg flex items-center justify-between">
          <p className="text-[#595959] truncate flex-1">{filePath}</p>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleOpenDirectory(filePath)}
            className="ml-2 h-8"
            title="打开文件所在目录"
          >
            <Folder className="h-4 w-4" />
          </Button>
        </div>

        {/* 基本信息 */}
        <div>
          <h3 className="text-sm font-medium text-[#262626] mb-3 flex items-center">
            <Info className="h-4 w-4 mr-1 text-[#1890FF]" />
            基本信息
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <p className="text-xs text-[#8C8C8C]">文件名</p>
              <p className="text-sm text-[#262626]">{getFileName(videoInfo.format.filename)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-[#8C8C8C]">时长</p>
              <p className="text-sm text-[#262626]">{formatDuration(videoInfo.format.duration)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-[#8C8C8C]">文件大小</p>
              <p className="text-sm text-[#262626]">{formatFileSize(videoInfo.format.size)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-[#8C8C8C]">总码率</p>
              <p className="text-sm text-[#262626]">{formatBitrate(videoInfo.format.bit_rate)}</p>
            </div>
          </div>
        </div>

        {/* 视频流信息 */}
        {videoStreams.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-[#262626] mb-3 flex items-center">
              <FileVideo className="h-4 w-4 mr-1 text-[#1890FF]" />
              视频流信息
            </h3>
            <div className="space-y-4">
              {videoStreams.map((stream, index) => (
                <div key={index} className="grid grid-cols-2 gap-4 p-3 bg-[#FAFAFA] rounded-lg">
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">编码格式</p>
                    <p className="text-sm text-[#262626]">{formatCodecName(stream.codec_name)}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">分辨率</p>
                    <p className="text-sm text-[#262626]">
                      {stream.width && stream.height ? `${stream.width}×${stream.height}` : '未知'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">帧率</p>
                    <p className="text-sm text-[#262626]">{formatFrameRate(stream.r_frame_rate || '')}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">码率</p>
                    <p className="text-sm text-[#262626]">{formatBitrate(stream.bit_rate || '')}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 音频流信息 */}
        {audioStreams.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-[#262626] mb-3 flex items-center">
              <Music className="h-4 w-4 mr-1 text-[#1890FF]" />
              音频流信息
            </h3>
            <div className="space-y-4">
              {audioStreams.map((stream, index) => (
                <div key={index} className="grid grid-cols-2 gap-4 p-3 bg-[#FAFAFA] rounded-lg">
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">编码格式</p>
                    <p className="text-sm text-[#262626]">{formatCodecName(stream.codec_name)}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">采样率</p>
                    <p className="text-sm text-[#262626]">
                      {stream.sample_rate ? `${stream.sample_rate} Hz` : '未知'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">声道数</p>
                    <p className="text-sm text-[#262626]">
                      {stream.channels ? `${stream.channels} 声道` : '未知'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">码率</p>
                    <p className="text-sm text-[#262626]">{formatBitrate(stream.bit_rate || '')}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    )
  }

  if (files.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <FileVideo className="h-8 w-8 mx-auto mb-2 text-gray-400" />
          <p className="text-sm text-gray-600">暂无视频文件</p>
        </div>
      </div>
    )
  }

  return (
    <TooltipProvider>
      <div className="flex h-full gap-4">
        {/* 左侧文件列表 */}
        <div className="w-80 flex-shrink-0">
          <div className="border border-gray-200 rounded-lg h-full flex flex-col bg-white shadow-sm">
            <div className="px-4 py-3 border-b border-gray-200 bg-gray-50/50">
              <h3 className="text-sm font-semibold text-gray-700">文件列表 ({files.length})</h3>
            </div>
            <div className="flex-1 overflow-y-auto">
              {files.map((file) => {
                const isLoading = loadingMap.get(file.path) || false
                const error = errorMap.get(file.path)

                return (
                  <div
                    key={file.path}
                    className={cn(
                      "flex items-center justify-between p-3 border-b border-gray-100 cursor-pointer transition-colors",
                      "hover:bg-gray-50",
                      selectedFile === file.path && "bg-blue-50 border-blue-200 shadow-sm"
                    )}
                    onClick={() => handleSelectFile(file.path)}
                  >
                    <div className="flex-1 min-w-0 flex items-center space-x-2">
                      {/* 状态指示器 */}
                      <div className="flex-shrink-0">
                        {isLoading && <Loader2 className="h-4 w-4 animate-spin text-blue-500" />}
                        {error && <AlertCircle className="h-4 w-4 text-red-500" />}
                        {!isLoading && !error && <FileVideo className="h-4 w-4 text-gray-400" />}
                      </div>

                      <div className="flex-1 min-w-0">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <p className="text-sm font-medium text-gray-900 truncate cursor-default">
                              {getFileName(file.path)}
                            </p>
                          </TooltipTrigger>
                          <TooltipContent side="top" className="max-w-xs">
                            <p className="break-all">{getFileName(file.path)}</p>
                          </TooltipContent>
                        </Tooltip>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <p className="text-xs text-gray-500 truncate cursor-default">
                              {file.path}
                            </p>
                          </TooltipTrigger>
                          <TooltipContent side="bottom" className="max-w-xs">
                            <p className="break-all">{file.path}</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                    </div>

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="ml-2 h-7 w-7 p-0 hover:bg-red-50 hover:text-red-600 transition-colors"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleRemoveFile(file.path)
                          }}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent side="top">
                        <p>从列表中移除</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                )
              })}
            </div>
          </div>
        </div>

        {/* 右侧详情区域 */}
        <div className="flex-1 min-w-0">
          <div className="border border-gray-200 rounded-lg h-full bg-white shadow-sm">
            {selectedFile ? (
              <div className="h-full flex flex-col">
                {/* 详情标题栏 */}
                <div className="px-4 py-3 border-b border-gray-200 bg-gray-50/50">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-semibold text-gray-700">详细信息</h3>
                    <div className="flex items-center space-x-1">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-7 w-7 p-0 hover:bg-green-50 hover:text-green-600 transition-colors"
                            onClick={() => handleOpenDirectory(selectedFile)}
                          >
                            <Folder className="h-3 w-3" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent side="top">
                          <p>打开文件所在目录</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  </div>
                </div>

                {/* 详情内容 */}
                <div className="flex-1 p-4 overflow-y-auto">
                  {renderVideoInfo(selectedFile)}
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <FileVideo className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                  <p className="text-sm text-gray-500 mb-1">请选择一个视频文件</p>
                  <p className="text-xs text-gray-400">点击左侧列表中的文件查看详细信息</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </TooltipProvider>
  )
}
