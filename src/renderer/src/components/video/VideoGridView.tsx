import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, CardTitle } from '@renderer/components/ui/card'
import { But<PERSON> } from '@renderer/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@renderer/components/ui/dialog'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@renderer/components/ui/tooltip'
import { X, Folder, RefreshCw, Loader2, AlertCircle, FileVideo, Music, Info, Eye } from 'lucide-react'
import { useVideoStore } from '@renderer/store/videoStore'
import { getFileName, formatBitrate, formatDuration, formatFileSize, formatFrameRate, formatCodecName } from '@renderer/utils/fileUtils'

interface VideoMetadata {
  format: {
    filename: string;
    duration: string;
    size: string;
    bit_rate: string;
    format_name?: string;
    format_long_name?: string;
    start_time?: string;
    nb_streams?: number;
    tags?: Record<string, string>;
  };
  streams: {
    codec_type: string;
    codec_name: string;
    width?: number;
    height?: number;
    display_aspect_ratio?: string;
    r_frame_rate?: string;
    avg_frame_rate?: string;
    bit_rate?: string;
    channels?: number;
    channel_layout?: string;
    sample_rate?: string;
  }[];
}

interface VideoGridViewProps {
  videoInfoMap: Map<string, VideoMetadata | null>;
  loadingMap: Map<string, boolean>;
  errorMap: Map<string, string | null>;
  onGetVideoInfo: (filePath: string) => void;
}

export function VideoGridView({
  videoInfoMap,
  loadingMap,
  errorMap,
  onGetVideoInfo
}: VideoGridViewProps) {
  const { files, removeFile } = useVideoStore()
  const [selectedDetailFile, setSelectedDetailFile] = useState<string | null>(null)

  const handleRemoveFile = (filePath: string) => {
    removeFile(filePath)
  }

  const handleOpenDirectory = (filePath: string) => {
    window.api.openFileDirectory(filePath)
  }

  const renderVideoCard = (file: { path: string }) => {
    const videoInfo = videoInfoMap.get(file.path)
    const isLoading = loadingMap.get(file.path) || false
    const error = errorMap.get(file.path)

    if (isLoading) {
      return (
        <Card key={file.path} className="h-72 border border-gray-200 shadow-sm hover:shadow-md transition-shadow bg-white">
          <CardHeader className="pb-3 px-4 pt-4">
            <CardTitle className="text-sm flex items-center justify-between">
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className="truncate font-semibold text-gray-900 cursor-default">
                    {getFileName(file.path)}
                  </span>
                </TooltipTrigger>
                <TooltipContent side="top" className="max-w-xs">
                  <p className="break-all">{getFileName(file.path)}</p>
                </TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-7 w-7 p-0 hover:bg-red-50 hover:text-red-600 transition-colors"
                    onClick={() => handleRemoveFile(file.path)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="top">
                  <p>从列表中移除</p>
                </TooltipContent>
              </Tooltip>
            </CardTitle>
          </CardHeader>
          <CardContent className="flex items-center justify-center h-40 px-4 pb-4">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-3 text-blue-500" />
              <p className="text-sm text-gray-600 font-medium">正在加载视频信息...</p>
              <p className="text-xs text-gray-400 mt-1">请稍候</p>
            </div>
          </CardContent>
        </Card>
      )
    }

    if (error) {
      return (
        <Card key={file.path} className="h-72 border border-red-200 shadow-sm hover:shadow-md transition-shadow bg-red-50/30">
          <CardHeader className="pb-3 px-4 pt-4">
            <CardTitle className="text-sm flex items-center justify-between">
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className="truncate font-semibold text-gray-900 cursor-default">
                    {getFileName(file.path)}
                  </span>
                </TooltipTrigger>
                <TooltipContent side="top" className="max-w-xs">
                  <p className="break-all">{getFileName(file.path)}</p>
                </TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-7 w-7 p-0 hover:bg-red-50 hover:text-red-600 transition-colors"
                    onClick={() => handleRemoveFile(file.path)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="top">
                  <p>从列表中移除</p>
                </TooltipContent>
              </Tooltip>
            </CardTitle>
          </CardHeader>
          <CardContent className="flex items-center justify-center h-40 px-4 pb-4">
            <div className="text-center">
              <AlertCircle className="h-8 w-8 mx-auto mb-3 text-red-500" />
              <p className="text-sm text-red-600 font-medium mb-3">加载失败</p>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onGetVideoInfo(file.path)}
                    className="h-8 text-sm border-red-200 text-red-600 hover:bg-red-50"
                  >
                    <RefreshCw className="h-3 w-3 mr-1" />
                    重试
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="top">
                  <p>重新获取视频信息</p>
                </TooltipContent>
              </Tooltip>
            </div>
          </CardContent>
        </Card>
      )
    }

    if (!videoInfo) {
      return (
        <Card key={file.path} className="h-72 border border-gray-200 shadow-sm hover:shadow-md transition-shadow bg-gray-50/30">
          <CardHeader className="pb-3 px-4 pt-4">
            <CardTitle className="text-sm flex items-center justify-between">
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className="truncate font-semibold text-gray-900 cursor-default">
                    {getFileName(file.path)}
                  </span>
                </TooltipTrigger>
                <TooltipContent side="top" className="max-w-xs">
                  <p className="break-all">{getFileName(file.path)}</p>
                </TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-7 w-7 p-0 hover:bg-red-50 hover:text-red-600 transition-colors"
                    onClick={() => handleRemoveFile(file.path)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="top">
                  <p>从列表中移除</p>
                </TooltipContent>
              </Tooltip>
            </CardTitle>
          </CardHeader>
          <CardContent className="flex items-center justify-center h-40 px-4 pb-4">
            <div className="text-center">
              <FileVideo className="h-8 w-8 mx-auto mb-3 text-gray-400" />
              <p className="text-sm text-gray-600 font-medium mb-3">暂无视频信息</p>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onGetVideoInfo(file.path)}
                    className="h-8 text-sm border-blue-200 text-blue-600 hover:bg-blue-50"
                  >
                    <Info className="h-3 w-3 mr-1" />
                    获取信息
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="top">
                  <p>获取视频详细信息</p>
                </TooltipContent>
              </Tooltip>
            </div>
          </CardContent>
        </Card>
      )
    }

    // 获取主要的视频流信息
    const videoStream = videoInfo.streams.find(stream => stream.codec_type === 'video')
    const audioStream = videoInfo.streams.find(stream => stream.codec_type === 'audio')

    return (
      <Card key={file.path} className="h-72 border border-gray-200 shadow-sm hover:shadow-lg transition-all duration-200 bg-white hover:border-blue-300">
        <CardHeader className="pb-3 px-4 pt-4">
          <CardTitle className="text-sm flex items-center justify-between">
            <Tooltip>
              <TooltipTrigger asChild>
                <span className="truncate font-semibold text-gray-900 cursor-default">
                  {getFileName(file.path)}
                </span>
              </TooltipTrigger>
              <TooltipContent side="top" className="max-w-xs">
                <p className="break-all">{getFileName(file.path)}</p>
              </TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 w-7 p-0 hover:bg-red-50 hover:text-red-600 transition-colors"
                  onClick={() => handleRemoveFile(file.path)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="top">
                <p>从列表中移除</p>
              </TooltipContent>
            </Tooltip>
          </CardTitle>
        </CardHeader>
        <CardContent className="px-4 pb-4">
          {/* 关键信息网格 */}
          <div className="grid grid-cols-2 gap-3 text-xs mb-4">
            <div className="bg-gray-50 p-2 rounded-md">
              <p className="text-gray-500 mb-1">分辨率</p>
              <p className="text-gray-900 font-semibold">
                {videoStream?.width && videoStream?.height
                  ? `${videoStream.width}×${videoStream.height}`
                  : '未知'}
              </p>
            </div>
            <div className="bg-gray-50 p-2 rounded-md">
              <p className="text-gray-500 mb-1">码率</p>
              <p className="text-gray-900 font-semibold">
                {formatBitrate(videoInfo.format.bit_rate)}
              </p>
            </div>
            <div className="bg-gray-50 p-2 rounded-md">
              <p className="text-gray-500 mb-1">时长</p>
              <p className="text-gray-900 font-semibold">
                {formatDuration(videoInfo.format.duration)}
              </p>
            </div>
            <div className="bg-gray-50 p-2 rounded-md">
              <p className="text-gray-500 mb-1">帧率</p>
              <p className="text-gray-900 font-semibold">
                {formatFrameRate(videoStream?.r_frame_rate || '')}
              </p>
            </div>
            <div className="bg-gray-50 p-2 rounded-md">
              <p className="text-gray-500 mb-1">格式</p>
              <p className="text-gray-900 font-semibold">
                {formatCodecName(videoStream?.codec_name || '')}
              </p>
            </div>
            <div className="bg-gray-50 p-2 rounded-md">
              <p className="text-gray-500 mb-1">大小</p>
              <p className="text-gray-900 font-semibold">
                {formatFileSize(videoInfo.format.size)}
              </p>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-between items-center pt-2 border-t border-gray-100">
            <Dialog>
              <Tooltip>
                <TooltipTrigger asChild>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-8 text-xs border-blue-200 text-blue-600 hover:bg-blue-50"
                      onClick={() => setSelectedDetailFile(file.path)}
                    >
                      <Eye className="h-3 w-3 mr-1" />
                      详细信息
                    </Button>
                  </DialogTrigger>
                </TooltipTrigger>
                <TooltipContent side="top">
                  <p>查看完整的视频信息</p>
                </TooltipContent>
              </Tooltip>
              <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="text-base">
                    {getFileName(file.path)} - 详细信息
                  </DialogTitle>
                </DialogHeader>
                {selectedDetailFile === file.path && renderDetailedInfo(file.path, videoInfo)}
              </DialogContent>
            </Dialog>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 hover:bg-green-50 hover:text-green-600 transition-colors"
                  onClick={() => handleOpenDirectory(file.path)}
                >
                  <Folder className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="top">
                <p>打开文件所在目录</p>
              </TooltipContent>
            </Tooltip>
          </div>
        </CardContent>
      </Card>
    )
  }

  const renderDetailedInfo = (filePath: string, videoInfo: VideoMetadata) => {
    const videoStreams = videoInfo.streams.filter(stream => stream.codec_type === 'video')
    const audioStreams = videoInfo.streams.filter(stream => stream.codec_type === 'audio')

    return (
      <div className="space-y-6">
        {/* 文件路径 */}
        <div className="p-3 bg-[#F0F2F5] rounded-lg">
          <p className="text-sm text-[#595959] break-all">{filePath}</p>
        </div>

        {/* 基本信息 */}
        <div>
          <h3 className="text-sm font-medium text-[#262626] mb-3 flex items-center">
            <Info className="h-4 w-4 mr-1 text-[#1890FF]" />
            基本信息
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <p className="text-xs text-[#8C8C8C]">文件名</p>
              <p className="text-sm text-[#262626]">{getFileName(videoInfo.format.filename)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-[#8C8C8C]">时长</p>
              <p className="text-sm text-[#262626]">{formatDuration(videoInfo.format.duration)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-[#8C8C8C]">文件大小</p>
              <p className="text-sm text-[#262626]">{formatFileSize(videoInfo.format.size)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-[#8C8C8C]">总码率</p>
              <p className="text-sm text-[#262626]">{formatBitrate(videoInfo.format.bit_rate)}</p>
            </div>
          </div>
        </div>

        {/* 视频流信息 */}
        {videoStreams.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-[#262626] mb-3 flex items-center">
              <FileVideo className="h-4 w-4 mr-1 text-[#1890FF]" />
              视频流信息
            </h3>
            <div className="space-y-4">
              {videoStreams.map((stream, index) => (
                <div key={index} className="grid grid-cols-2 gap-4 p-3 bg-[#FAFAFA] rounded-lg">
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">编码格式</p>
                    <p className="text-sm text-[#262626]">{formatCodecName(stream.codec_name)}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">分辨率</p>
                    <p className="text-sm text-[#262626]">
                      {stream.width && stream.height ? `${stream.width}×${stream.height}` : '未知'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">帧率</p>
                    <p className="text-sm text-[#262626]">{formatFrameRate(stream.r_frame_rate || '')}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">码率</p>
                    <p className="text-sm text-[#262626]">{formatBitrate(stream.bit_rate || '')}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 音频流信息 */}
        {audioStreams.length > 0 && (
          <div>
            <h3 className="text-sm font-medium text-[#262626] mb-3 flex items-center">
              <Music className="h-4 w-4 mr-1 text-[#1890FF]" />
              音频流信息
            </h3>
            <div className="space-y-4">
              {audioStreams.map((stream, index) => (
                <div key={index} className="grid grid-cols-2 gap-4 p-3 bg-[#FAFAFA] rounded-lg">
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">编码格式</p>
                    <p className="text-sm text-[#262626]">{formatCodecName(stream.codec_name)}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">采样率</p>
                    <p className="text-sm text-[#262626]">
                      {stream.sample_rate ? `${stream.sample_rate} Hz` : '未知'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">声道数</p>
                    <p className="text-sm text-[#262626]">
                      {stream.channels ? `${stream.channels} 声道` : '未知'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-[#8C8C8C]">码率</p>
                    <p className="text-sm text-[#262626]">{formatBitrate(stream.bit_rate || '')}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    )
  }

  if (files.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <FileVideo className="h-8 w-8 mx-auto mb-2 text-gray-400" />
          <p className="text-sm text-gray-600">暂无视频文件</p>
        </div>
      </div>
    )
  }

  return (
    <TooltipProvider>
      <div className="h-full overflow-y-auto">
        {/* 优化的网格布局，与拖拽区域对齐 */}
        <div className="py-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
            {files.map((file) => renderVideoCard(file))}
          </div>
        </div>
      </div>
    </TooltipProvider>
  )
}
