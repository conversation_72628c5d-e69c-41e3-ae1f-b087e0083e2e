import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Progress } from "@/components/ui/progress"

interface VideoConvertFormProps {
	outputFormat: string
	onFormatChange: (format: string) => void
	saveDirectory: string
	onSaveDirectoryChange: (directory: string) => void
	deleteOriginal: boolean
	onDeleteOriginalChange: (deleteOriginal: boolean) => void
	onSelectDirectory: () => void
	onConvert: () => void
	disabled: boolean
	isProcessing: boolean
	onStop?: () => void
	progress?: number
}

export function VideoConvertForm({
	outputFormat,
	onFormatChange,
	saveDirectory,
	onSaveDirectoryChange,
	deleteOriginal,
	onDeleteOriginalChange,
	onSelectDirectory,
	onConvert,
	disabled,
	isProcessing,
	onStop,
	progress = 0
}: VideoConvertFormProps) {
	const formats = [
		{ value: 'mp4', label: 'MP4 (常用视频格式)' },
		{ value: 'avi', label: 'AVI (兼容性好)' },
		{ value: 'mkv', label: 'MKV (支持多轨道)' },
		{ value: 'mov', label: 'MOV (苹果设备)' },
		{ value: 'webm', label: 'WEBM (网页视频)' },
		{ value: 'flv', label: 'FLV (Flash视频)' }
	]

	return (
		<div className="space-y-4">
			<div className="space-y-4 w-full">
				<div className="space-y-2 w-full">
					<Label htmlFor="output-format">输出格式</Label>
					<Select
						value={outputFormat}
						onValueChange={onFormatChange}
						disabled={isProcessing}
					>
						<SelectTrigger id="output-format">
							<SelectValue placeholder="选择输出格式" />
						</SelectTrigger>
						<SelectContent>
							{formats.map(format => (
								<SelectItem key={format.value} value={format.value}>
									{format.label}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>

				<div className="space-y-2 w-full">
					<Label htmlFor="save-directory">保存目录</Label>
					<div className="flex w-full">
						<Input
							id="save-directory"
							placeholder="默认与原视频相同目录"
							className="rounded-r-none"
							value={saveDirectory}
							onChange={(e) => onSaveDirectoryChange(e.target.value)}
							readOnly
							disabled={isProcessing}
						/>
						<Button
							variant="secondary"
							className="rounded-l-none"
							onClick={onSelectDirectory}
							disabled={isProcessing}
						>
							浏览
						</Button>
					</div>
				</div>
			</div>

			<div className="flex items-center space-x-2 w-full">
				<Checkbox
					id="delete-original"
					checked={deleteOriginal}
					onCheckedChange={onDeleteOriginalChange}
					disabled={isProcessing}
				/>
				<Label
					htmlFor="delete-original"
					className="text-sm font-normal cursor-pointer"
				>
					转换完成后删除原视频
				</Label>
			</div>

			{isProcessing && (
				<div className="space-y-2 w-full">
					<Progress value={progress} />
					<p className="text-xs text-[#8C8C8C] text-right">{Math.round(progress)}%</p>
				</div>
			)}

			<div className="flex justify-end space-x-2 w-full">
				{isProcessing && onStop ? (
					<Button
						className="mt-4 w-full flex items-center justify-center gap-2 bg-red-500 hover:bg-red-600 text-white py-2 rounded-lg text-sm"
						onClick={onStop}
					>
						停止转换
					</Button>
				) : (
					<Button
						onClick={onConvert}
						disabled={disabled}
						className="w-full"
					>
						开始转换
					</Button>
				)}
			</div>
		</div>
	)
}
