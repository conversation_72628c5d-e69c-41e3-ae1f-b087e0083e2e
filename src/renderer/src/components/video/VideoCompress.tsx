export function VideoCompress(): JSX.Element {
  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h2 className="text-[#262626] text-xl font-medium">视频压缩</h2>
        <p className="text-[#8C8C8C] text-sm mt-1">压缩视频文件大小，支持调整分辨率、帧率等参数</p>
      </div>

      {/* 操作区 */}
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <button className="bg-[#1890FF] text-white px-4 py-2 rounded-lg hover:bg-[#40A9FF] transition-colors">
          选择文件
        </button>
      </div>

      {/* 参数设置区 */}
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h3 className="text-[#262626] text-base font-medium mb-4">压缩参数</h3>
        {/* 参数表单 */}
      </div>

      {/* 文件列表区 */}
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h3 className="text-[#262626] text-base font-medium mb-4">文件列表</h3>
        {/* 文件列表 */}
      </div>
    </div>
  )
}
