import { Button } from "@renderer/components/ui/button"
import { Input } from "@renderer/components/ui/input"
import { Label } from "@renderer/components/ui/label"
import { Card } from "@renderer/components/ui/card"

interface VideoTrimFormProps {
	settings: {
		startTime: string
		endTime: string
		saveDirectory: string
	}
	onSettingsChange: (settings: any) => void
	onSelectDirectory: () => void
	onTrim: () => void
	selectedFile: string | null
	disabled: boolean
}

export function VideoTrimForm({
	settings,
	onSettingsChange,
	onSelectDirectory,
	onTrim,
	selectedFile,
	disabled
}: VideoTrimFormProps) {
	return (
		<div className="space-y-4">
			{selectedFile && (
				<Card className="p-3 bg-[#F0F2F5]">
					<p className="text-[#595959] truncate">当前选中: {selectedFile}</p>
				</Card>
			)}

			<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
				<div className="space-y-2">
					<Label htmlFor="start-time">开始时间 (HH:MM:SS)</Label>
					<Input
						id="start-time"
						type="text"
						value={settings.startTime}
						onChange={(e) => onSettingsChange({ startTime: e.target.value })}
						placeholder="00:00:00"
						pattern="[0-9]{2}:[0-9]{2}:[0-9]{2}"
					/>
				</div>

				<div className="space-y-2">
					<Label htmlFor="end-time">结束时间 (HH:MM:SS)</Label>
					<Input
						id="end-time"
						type="text"
						value={settings.endTime}
						onChange={(e) => onSettingsChange({ endTime: e.target.value })}
						placeholder="00:01:00"
						pattern="[0-9]{2}:[0-9]{2}:[0-9]{2}"
					/>
				</div>
			</div>

			<div className="space-y-2">
				<Label htmlFor="save-directory">保存目录</Label>
				<div className="flex">
					<Input
						id="save-directory"
						value={settings.saveDirectory}
						placeholder="选择保存目录"
						className="rounded-r-none"
						readOnly
					/>
					<Button
						variant="secondary"
						className="rounded-l-none"
						onClick={onSelectDirectory}
					>
						浏览
					</Button>
				</div>
			</div>

			<div className="flex justify-end">
				<Button
					onClick={onTrim}
					disabled={disabled}
				>
					开始截取
				</Button>
			</div>
		</div>
	)
}
