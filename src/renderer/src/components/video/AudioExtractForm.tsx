import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"
import { Headphones } from "lucide-react"

interface AudioExtractFormProps {
  settings: {
    outputFormat: 'mp3' | 'aac' | 'wav' | 'flac' | 'ogg'
    bitrate: string
    sampleRate: string
    channels: number
    outputDirectory: string
  }
  onSettingsChange: (settings: any) => void
  onSelectDirectory: () => void
  onExtract: () => void
  onStop?: () => void
  disabled: boolean
  isProcessing: boolean
  progress?: number
}

export function AudioExtractForm({
  settings,
  onSettingsChange,
  onSelectDirectory,
  onExtract,
  onStop,
  disabled,
  isProcessing,
  progress = 0
}: AudioExtractFormProps) {
  const formats = [
    { value: 'mp3', label: 'MP3 (常用音乐格式)' },
    { value: 'aac', label: 'AAC (高质量音频)' },
    { value: 'wav', label: 'WAV (无损音频)' },
    { value: 'flac', label: 'FLAC (无损压缩)' },
    { value: 'ogg', label: 'OGG (开放格式)' }
  ]

  const bitrates = [
    { value: '128k', label: '128 kbps (一般质量)' },
    { value: '192k', label: '192 kbps (良好质量)' },
    { value: '256k', label: '256 kbps (高质量)' },
    { value: '320k', label: '320 kbps (最高质量)' }
  ]

  const sampleRates = [
    { value: '22050', label: '22.05 kHz (低质量)' },
    { value: '44100', label: '44.1 kHz (CD质量)' },
    { value: '48000', label: '48 kHz (DVD质量)' },
    { value: '96000', label: '96 kHz (高清质量)' }
  ]

  const channelOptions = [
    { value: 1, label: '单声道' },
    { value: 2, label: '立体声' }
  ]

  return (
    <div className="space-y-4">
      <div className="space-y-4 w-full">
        <div className="space-y-2 w-full">
          <Label htmlFor="outputFormat">输出格式</Label>
          <Select
            value={settings.outputFormat}
            onValueChange={(value) => onSettingsChange({ outputFormat: value })}
            disabled={isProcessing}
          >
            <SelectTrigger id="outputFormat">
              <SelectValue placeholder="选择输出格式" />
            </SelectTrigger>
            <SelectContent>
              {formats.map(format => (
                <SelectItem key={format.value} value={format.value}>
                  {format.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2 w-full">
          <Label htmlFor="bitrate">比特率</Label>
          <Select
            value={settings.bitrate}
            onValueChange={(value) => onSettingsChange({ bitrate: value })}
            disabled={isProcessing || settings.outputFormat === 'wav' || settings.outputFormat === 'flac'}
          >
            <SelectTrigger id="bitrate">
              <SelectValue placeholder="选择比特率" />
            </SelectTrigger>
            <SelectContent>
              {bitrates.map(bitrate => (
                <SelectItem key={bitrate.value} value={bitrate.value}>
                  {bitrate.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2 w-full">
          <Label htmlFor="sampleRate">采样率</Label>
          <Select
            value={settings.sampleRate}
            onValueChange={(value) => onSettingsChange({ sampleRate: value })}
            disabled={isProcessing}
          >
            <SelectTrigger id="sampleRate">
              <SelectValue placeholder="选择采样率" />
            </SelectTrigger>
            <SelectContent>
              {sampleRates.map(rate => (
                <SelectItem key={rate.value} value={rate.value}>
                  {rate.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2 w-full">
          <Label htmlFor="channels">声道</Label>
          <Select
            value={settings.channels.toString()}
            onValueChange={(value) => onSettingsChange({ channels: Number(value) })}
            disabled={isProcessing}
          >
            <SelectTrigger id="channels">
              <SelectValue placeholder="选择声道数" />
            </SelectTrigger>
            <SelectContent>
              {channelOptions.map(option => (
                <SelectItem key={option.value} value={option.value.toString()}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2 w-full">
        <Label htmlFor="outputDirectory">输出目录</Label>
        <div className="flex w-full">
          <Input
            id="outputDirectory"
            value={settings.outputDirectory}
            placeholder="默认与源文件相同目录"
            className="rounded-r-none"
            readOnly
            disabled={isProcessing}
          />
          <Button
            variant="secondary"
            className="rounded-l-none"
            onClick={onSelectDirectory}
            disabled={isProcessing}
          >
            浏览
          </Button>
        </div>
      </div>

      {isProcessing && (
        <div className="space-y-2 w-full">
          <Progress value={progress} />
          <p className="text-xs text-[#8C8C8C] text-right">{Math.round(progress)}%</p>
        </div>
      )}

      <div className="flex justify-end space-x-2 w-full">
        {isProcessing && onStop ? (
          <Button
            variant="destructive"
            className="w-full"
            onClick={onStop}
          >
            停止提取
          </Button>
        ) : (
          <Button
            onClick={onExtract}
            disabled={disabled}
            className="w-full"
          >
            <Headphones className="mr-2 h-4 w-4" />
            提取音频
          </Button>
        )}
      </div>
    </div>
  )
}
