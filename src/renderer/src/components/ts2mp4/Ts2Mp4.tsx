export function Ts2Mp4(): JSX.Element {
  const selectDirectory = async () => {
  const directory = await window.api.selectDirectory()
  if (directory){
    console.log("%c Line:5 🥥 directory", "font-size:18px;color:#ffffff;background:#3f7cff", directory);

  }
  }

  const ts2mp4 = () => {
    window.api.ts2mp4()
  }
  return <div>
    <h1>ts 转 MP4</h1>
    <button className="btn" onClick={selectDirectory}>选择目录</button>
    <button className="btn" onClick={ts2mp4}>转换</button>
  </div>
}
