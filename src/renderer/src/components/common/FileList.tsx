import { X, Folder } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { getFormatIcon } from '@renderer/utils/fileIcons'

// 定义文件对象接口
interface FileObject {
  path: string
  status?: number | string
}

interface FileListProps {
  files: string[]
  storeFiles: FileObject[] // 直接接收 store 中的文件对象数组
  onRemove: (filePath: string) => void
  isProcessing?: boolean
  selectedFile?: string | null
  onSelect?: (filePath: string) => void
  onOpenDirectory?: (filePath: string) => void
}

export function FileList({
  files,
  storeFiles,
  onRemove,
  isProcessing = false,
  selectedFile = null,
  onSelect,
  onOpenDirectory
}: FileListProps) {
  if (files.length === 0) {
    return (
      <div className="text-center py-8 text-[#8C8C8C]">
        暂无文件，请先上传文件
      </div>
    )
  }

  // 提取文件名的辅助函数
  const getFileName = (filePath: string) => {
    // 处理不同操作系统的路径分隔符
    const normalizedPath = filePath.replace(/\\/g, '/');
    return normalizedPath.split('/').pop() || filePath;
  };

  // 打开文件所在目录
  const handleOpenDirectory = (filePath: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (onOpenDirectory) {
      onOpenDirectory(filePath);
    } else {
      window.api.openFileDirectory(filePath);
    }
  };

  // 获取文件状态的辅助函数
  const getFileStatus = (filePath: string) => {
    const fileObj = storeFiles.find(file => file.path === filePath);
    return fileObj?.status;
  };



  return (
    <div className="space-y-2">
      {files.map((file, index) => {
        const fileName = getFileName(file)
        const isSelected = selectedFile === file
        const fileStatus = getFileStatus(file)

        return (
          <div
            key={index}
            className={`relative border rounded p-3 space-y-1 ${isSelected ? 'bg-blue-50 border-blue-500' : 'bg-gray-50 border-slate-100 hover:border-blue-300'}`}
            onClick={() => onSelect && onSelect(file)}
          >
            <div className="flex items-center justify-between text-sm font-medium text-gray-800">
              <div className="flex items-center">
                <span className="mr-2">{getFormatIcon(fileName)}</span>
                <span className="truncate">{fileName}</span>
              </div>

              {/* 文件状态显示 */}
              {fileStatus === 100 && <span className="text-blue-500">100%</span>}
              {fileStatus === -1 && <span className="text-red-500">失败</span>}
            </div>

            {typeof fileStatus === 'number' && fileStatus > 0 && fileStatus < 100 && (
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-blue-500 h-2 rounded-full" style={{ width: `${fileStatus}%` }}></div>
              </div>
            )}

            <div className="flex justify-between items-center">
              <span className="text-gray-400 text-xs truncate ">{file}</span>
            </div>

            <div className="flex items-center absolute right-2 top-1/2 -translate-y-1/2">
              <Button
                variant="ghost"
                size="icon"
                onClick={(e) => handleOpenDirectory(file, e)}
                disabled={isProcessing && selectedFile === file}
                className="p-1 text-gray-500 hover:text-blue-500 hover:bg-blue-100 rounded-full"
                title="打开文件所在目录"
              >
                <Folder className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={(e) => {
                  e.stopPropagation()
                  onRemove(file)
                }}
                disabled={isProcessing && selectedFile === file}
                className="ml-1 p-1 text-gray-500 hover:text-red-500 hover:bg-red-100 rounded-full"
                title="移除文件"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
        )
      })}
    </div>
  )
}
