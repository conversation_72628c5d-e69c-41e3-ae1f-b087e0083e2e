
import { Button } from '@/components/ui/button'
import { FileList } from '@renderer/components/common/FileList'
import { DragDropOverlay } from '@renderer/components/common/DragDropOverlay'
import { useImageStore } from '@renderer/store/imageStore'
import { useVideoStore } from '@renderer/store/videoStore'
import { useShallow } from 'zustand/react/shallow';
import { Trash2 } from 'lucide-react';
import { toast } from 'sonner';

// 定义 store 类型
type StoreType = 'image' | 'video'

interface FileUploadListProps {
  storeType: StoreType
  title?: string
  acceptFileTypes?: 'video/*' | 'image/*' | '*'
  extraButton?: React.ReactNode
  multiple?: boolean
}

export function FileUploadList({
  storeType,
  title = '文件列表',
  acceptFileTypes = storeType === 'image' ? 'image/*' : 'video/*',
  extraButton,
  multiple = true
}: FileUploadListProps) {
  // 根据 storeType 选择对应的 store
  const useStore = storeType === 'image' ? useImageStore : useVideoStore

  // 从 store 中获取状态和方法
  const {
    files: storeFiles,
    currentFile,
    isProcessing,
    addFiles,
    clearFiles,
    removeFile,
    setCurrentFile
  } = useStore(useShallow(state => {
    return {
      files: state.files,
      currentFile: state.currentFile,
      isProcessing: state.isProcessing,
      addFiles: state.addFiles,
      clearFiles: state.clearFiles,
      removeFile: state.removeFile,
      setCurrentFile: state.setCurrentFile
    }
  }))

  // 将 store 中的文件对象转换为路径数组
  const files = storeFiles.map(file => file.path)

  // 处理打开文件所在目录
  const handleOpenDirectory = (filePath: string) => {
    window.api.openFileDirectory(filePath)
  }

  // 处理清空文件列表
  const handleClearFiles = () => {
    if (files.length === 0) return;

    // 如果正在处理文件，显示警告
    if (isProcessing) {
      toast.error('无法清空文件列表', { description: '当前有文件正在处理中' });
      return;
    }

    // 显示确认对话框
    if (window.confirm(`确定要清空所有${files.length}个文件吗？`)) {
      clearFiles();
      toast.success('文件列表已清空');
    }
  };

  const handleFileSelect = async () => {
    try {
      const fileType = acceptFileTypes === 'video/*'
        ? 'video'
        : acceptFileTypes === 'image/*'
          ? 'image'
          : 'all'

      // 获取选择的文件，传递 multiple 参数
      const selectedFiles = await window.api.selectFile(fileType, multiple)

      if (selectedFiles && selectedFiles.length > 0) {
        // 如果是单选模式，清空现有文件
        if (!multiple && files.length > 0) {
          clearFiles()
        }
        // 添加选择的文件
        addFiles(selectedFiles)
      }
    } catch (error) {
      console.error('Error selecting files:', error)
    }
  }

  return (
    <div className="h-full flex flex-col overflow-hidden">
      <div className="flex-none p-4 pb-2">
        <div className="flex justify-between items-center">
          <span className="font-medium">{title}</span>
          <div className="flex items-center space-x-2">
            {extraButton}
            {files.length > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearFiles}
                className="h-8"
                disabled={isProcessing}
                title="清空文件列表"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                清空
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={handleFileSelect}
              className="h-8"
              disabled={isProcessing}
            >
              选择文件
            </Button>
          </div>
        </div>
      </div>

      <div className="flex-1 p-4 pt-0 overflow-auto">
        {/* 拖放覆盖层 */}
        <DragDropOverlay
          onFileDrop={addFiles}
          acceptFileTypes={acceptFileTypes}
          multiple={multiple}
        />

        {/* 文件列表 */}
        {files.length > 0 && (
          <div className="mb-4">
            <FileList
              files={files}
              storeFiles={storeFiles}
              onRemove={removeFile}
              isProcessing={isProcessing}
              onOpenDirectory={handleOpenDirectory}
              selectedFile={currentFile}
              onSelect={setCurrentFile}
            />
          </div>
        )}

        <div
          className="relative flex flex-col items-center justify-center w-full h-48 px-4 py-6 border-2 border-dashed border-gray-300 rounded-xl bg-white text-gray-500 hover:border-blue-500 hover:bg-blue-50 transition-colors duration-200 cursor-pointer"
          onClick={handleFileSelect}
        >
          + 添加文件<br />
          <span className="text-gray-400 text-sm">或将文件拖拽到此处</span>
        </div>
      </div>
    </div>
  )
}
