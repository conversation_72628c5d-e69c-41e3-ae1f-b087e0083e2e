import { Upload } from 'lucide-react'
import { useState } from 'react'
import { cn } from '@renderer/lib/utils'
import { Button } from '@renderer/components/ui/button'

interface FileUploaderProps {
	onUpload: () => void
	accept?: string
	multiple?: boolean
}

export function FileUploader({ onUpload, accept = '*', multiple = true }: FileUploaderProps) {
	const [isDragging, setIsDragging] = useState(false)

	const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
		e.preventDefault()
		setIsDragging(true)
	}

	const handleDragLeave = () => {
		setIsDragging(false)
	}

	const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
		e.preventDefault()
		setIsDragging(false)
		// 实际的拖放处理将在后续实现
		// 目前只调用onUpload
		onUpload()
	}

	return (
		<div
			className={cn(
				"border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors",
				isDragging ? "border-[#1890FF] bg-[#E6F7FF]" : "border-[#D9D9D9] hover:border-[#1890FF] hover:bg-[#E6F7FF]"
			)}
			onClick={onUpload}
			onDragOver={handleDragOver}
			onDragLeave={handleDragLeave}
			onDrop={handleDrop}
		>
			<div className="flex flex-col items-center">
				<Upload className="w-12 h-12 text-[#1890FF] mb-2" />
				<p className="text-[#262626] font-medium">点击或拖拽文件到此区域上传</p>
				<p className="text-[#8C8C8C] text-sm mt-1">
					{multiple ? '支持多文件上传' : '单文件上传'}
				</p>
				<Button
					variant="default"
					className="mt-4"
					onClick={(e) => {
						e.stopPropagation()
						onUpload()
					}}
				>
					选择文件
				</Button>
			</div>
		</div>
	)
}
