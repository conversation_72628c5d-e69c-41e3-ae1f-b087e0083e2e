import * as React from "react"
import { Link, useMatches } from "@tanstack/react-router"
import { cn } from "@renderer/lib/utils"
import { ArrowRight, ArrowRightSquare, ChevronRight, LucideIcon } from "lucide-react"

interface NavProps extends React.HTMLAttributes<HTMLElement> {
  items: {
    href: string
    title: string
    icon?: LucideIcon
  }[]
  title?: string
}

export function Nav({ className, items, title, ...props }: NavProps) {
  const matches = useMatches()
  const currentPath = matches.length > 0 ? matches[matches.length - 1].pathname : ""

  return (
    <div className={cn("pb-4", className)} {...props}>
      {title && (
        <h2 className="px-4 py-2 text-slate-500 text-sm font-medium">{title}</h2>
      )}
      <nav className="grid gap-3 px-6">
        {items.map((item, index) => {
          const Icon = item.icon
          const isActive = currentPath === item.href

          return (
            <Link
              key={index}
              to={item.href}
              className={cn(
                "flex items-center justify-between gap-4 rounded-md px-3 py-3 text-sm font-medium transition-colors",
                "hover:bg-blue-200 bg-white text-slate-600 group",
                isActive
                  ? "bg-blue-500 text-white hover:bg-blue-500 hover:text-white"
                  : ""
              )}
            >
              <div className="inline-flex gap-3 items-center">

                {Icon && <Icon className="h-4 w-4 " />}
                <span>{item.title}</span>
              </div>

              <ChevronRight className="" size={14} />
            </Link>
          )
        })}
      </nav>
    </div>
  )
}
