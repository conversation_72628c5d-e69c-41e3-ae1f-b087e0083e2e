import React from 'react'
import { cn } from '@/lib/utils'

interface ColorPickerProps extends React.InputHTMLAttributes<HTMLInputElement> {
  color: string
  onChange: (color: string) => void
  disabled?: boolean
}

export function ColorPicker({ color, onChange, disabled, className, ...props }: ColorPickerProps) {
  return (
    <div className={cn("relative w-10 h-10", className)}>
      <div 
        className="absolute inset-0 rounded-md border border-input overflow-hidden"
        style={{ backgroundColor: color }}
      />
      <input
        type="color"
        value={color}
        onChange={(e) => onChange(e.target.value)}
        disabled={disabled}
        className="opacity-0 absolute inset-0 w-full h-full cursor-pointer"
        {...props}
      />
    </div>
  )
}
