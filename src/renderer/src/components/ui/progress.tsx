import * as React from "react"

import { cn } from "@renderer/lib/utils"

const Progress = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & { value?: number; max?: number }
>(({ className, value, max = 100, ...props }, ref) => {
  const percentage = value != null ? (value / max) * 100 : 0

  return (
    <div
      ref={ref}
      className={cn(
        "h-2 w-full overflow-hidden rounded-full bg-[#F0F2F5]",
        className
      )}
      {...props}
    >
      <div
        className="h-full bg-[#1890FF] transition-all"
        style={{ width: `${percentage}%` }}
      />
    </div>
  )
})
Progress.displayName = "Progress"

export { Progress }
