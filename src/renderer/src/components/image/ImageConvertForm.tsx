import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Progress } from "@/components/ui/progress"

interface ImageConvertFormProps {
	outputFormat: string
	onFormatChange: (format: string) => void
	saveDirectory: string
	onSaveDirectoryChange?: (directory: string) => void
	deleteOriginal?: boolean
	onDeleteOriginalChange?: (deleteOriginal: boolean) => void
	onSelectDirectory: () => void
	onConvert: () => void
	disabled: boolean
	isProcessing?: boolean
	onStop?: () => void
	progress?: number
	processedCount?: number
	totalCount?: number
}

export function ImageConvertForm({
	outputFormat,
	onFormatChange,
	saveDirectory,
	onSaveDirectoryChange,
	deleteOriginal = false,
	onDeleteOriginalChange,
	onSelectDirectory,
	onConvert,
	disabled,
	isProcessing = false,
	onStop,
	progress = 0,
	processedCount = 0,
	totalCount = 0
}: ImageConvertFormProps) {
	const formats = [
		{ value: 'jpg', label: 'JPG (常用照片格式)' },
		{ value: 'png', label: 'PNG (支持透明背景)' },
		{ value: 'webp', label: 'WEBP (高压缩比网页图片)' },
		{ value: 'gif', label: 'GIF (支持动画)' },
		{ value: 'bmp', label: 'BMP (无损位图)' }
	]

	return (
		<div className="space-y-4">
			<div className="space-y-4 w-full">
				<div className="space-y-2 w-full">
					<Label htmlFor="output-format">输出格式</Label>
					<Select
						value={outputFormat}
						onValueChange={onFormatChange}
						disabled={isProcessing}
					>
						<SelectTrigger id="output-format">
							<SelectValue placeholder="选择输出格式" />
						</SelectTrigger>
						<SelectContent>
							{formats.map(format => (
								<SelectItem key={format.value} value={format.value}>
									{format.label}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>

				<div className="space-y-2 w-full">
					<Label htmlFor="save-directory">保存目录</Label>
					<div className="flex w-full">
						<Input
							id="save-directory"
							value={saveDirectory}
							placeholder="默认与原图片相同目录"
							className="rounded-r-none"
							readOnly
							disabled={isProcessing}
							onChange={(e) => onSaveDirectoryChange && onSaveDirectoryChange(e.target.value)}
						/>
						<Button
							variant="secondary"
							className="rounded-l-none"
							onClick={onSelectDirectory}
							disabled={isProcessing}
						>
							浏览
						</Button>
					</div>
				</div>
			</div>

			<div className="flex items-center space-x-2 w-full">
				<Checkbox
					id="delete-original"
					checked={deleteOriginal}
					onCheckedChange={(checked) => onDeleteOriginalChange && onDeleteOriginalChange(checked as boolean)}
					disabled={isProcessing}
				/>
				<Label
					htmlFor="delete-original"
					className="text-sm font-normal cursor-pointer"
				>
					转换完成后删除原图片
				</Label>
			</div>

			{isProcessing && (
				<div className="space-y-2 w-full">
					<Progress value={progress} />
					<div className="flex justify-between">
						<p className="text-xs text-[#8C8C8C]">
							{totalCount > 0 ? `已处理 ${processedCount}/${totalCount} 个文件` : ''}
						</p>
						<p className="text-xs text-[#8C8C8C]">{Math.round(progress)}%</p>
					</div>
				</div>
			)}

			<div className="flex justify-end space-x-2 w-full">
				{isProcessing && onStop ? (
					<Button
						className="mt-4 w-full flex items-center justify-center gap-2 bg-red-500 hover:bg-red-600 text-white py-2 rounded-lg text-sm"
						onClick={onStop}
					>
						停止转换
					</Button>
				) : (
					<Button
						onClick={onConvert}
						disabled={disabled}
						className="w-full"
					>
						开始转换
					</Button>
				)}
			</div>
		</div>
	)
}
