import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Progress } from "@/components/ui/progress"
import { Checkbox } from "@/components/ui/checkbox"
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select"

interface ImageEnhanceFormProps {
	settings: {
		scale: number
		sharpness: number
		denoise: boolean
		denoiseLevel: number
		saveDirectory: string
		deleteOriginal?: boolean
	}
	onSettingsChange: (settings: any) => void
	onSelectDirectory: () => void
	onEnhance: () => void
	onStop?: () => void
	disabled: boolean
	isProcessing: boolean
	progress?: number
	processedCount?: number
	totalCount?: number
}

export function ImageEnhanceForm({
	settings,
	onSettingsChange,
	onSelectDirectory,
	onEnhance,
	onStop,
	disabled,
	isProcessing,
	progress = 0,
	processedCount = 0,
	totalCount = 0
}: ImageEnhanceFormProps) {
	// 放大倍数选项
	const scaleOptions = [
		{ value: 1.5, label: '1.5x (轻微放大)' },
		{ value: 2, label: '2x (标准放大)' },
		{ value: 3, label: '3x (高倍放大)' },
		{ value: 4, label: '4x (超级放大)' }
	]

	return (
		<div className="space-y-4">
			<div className="space-y-2 w-full">
				<Label htmlFor="scale">放大倍数</Label>
				<Select
					value={settings.scale.toString()}
					onValueChange={(value) => onSettingsChange({ scale: parseFloat(value) })}
					disabled={isProcessing}
				>
					<SelectTrigger id="scale" className="w-full">
						<SelectValue placeholder="选择放大倍数" />
					</SelectTrigger>
					<SelectContent>
						{scaleOptions.map((option) => (
							<SelectItem key={option.value} value={option.value.toString()}>
								{option.label}
							</SelectItem>
						))}
					</SelectContent>
				</Select>
				<p className="text-xs text-[#8C8C8C] mt-1">
					放大倍数越高，处理时间越长，文件尺寸也会相应增加
				</p>
			</div>

			<div className="space-y-2 w-full">
				<Label htmlFor="sharpness">锐化程度 ({settings.sharpness}%)</Label>
				<Slider
					id="sharpness"
					min={0}
					max={100}
					value={[settings.sharpness]}
					onValueChange={(value) => onSettingsChange({ sharpness: value[0] })}
					disabled={isProcessing}
				/>
				<div className="flex justify-between text-xs text-[#8C8C8C] mt-1">
					<span>无锐化</span>
					<span>最大锐化</span>
				</div>
			</div>

			<div className="space-y-2 w-full">
				<div className="flex items-center space-x-2">
					<Checkbox
						id="denoise"
						checked={settings.denoise}
						onCheckedChange={(checked) => onSettingsChange({ denoise: !!checked })}
						disabled={isProcessing}
					/>
					<Label
						htmlFor="denoise"
						className="text-sm font-normal cursor-pointer"
					>
						应用降噪（减少图片噪点）
					</Label>
				</div>

				{settings.denoise && (
					<div className="mt-2 pl-6">
						<Label htmlFor="denoiseLevel" className="text-sm">降噪级别 ({settings.denoiseLevel}%)</Label>
						<Slider
							id="denoiseLevel"
							min={0}
							max={100}
							value={[settings.denoiseLevel]}
							onValueChange={(value) => onSettingsChange({ denoiseLevel: value[0] })}
							disabled={isProcessing}
							className="mt-2"
						/>
						<div className="flex justify-between text-xs text-[#8C8C8C] mt-1">
							<span>轻微降噪</span>
							<span>强力降噪</span>
						</div>
					</div>
				)}
			</div>

			<div className="space-y-2 w-full">
				<Label htmlFor="saveDirectory">保存目录</Label>
				<div className="flex w-full">
					<Input
						id="saveDirectory"
						value={settings.saveDirectory}
						placeholder="选择保存目录（默认与原图相同）"
						className="rounded-r-none"
						readOnly
						disabled={isProcessing}
					/>
					<Button
						variant="secondary"
						className="rounded-l-none"
						onClick={onSelectDirectory}
						disabled={isProcessing}
					>
						浏览
					</Button>
				</div>
			</div>

			<div className="flex items-center space-x-2 w-full">
				<Checkbox
					id="deleteOriginal"
					checked={settings.deleteOriginal}
					onCheckedChange={(checked) => onSettingsChange({ deleteOriginal: !!checked })}
					disabled={isProcessing}
				/>
				<Label
					htmlFor="deleteOriginal"
					className="text-sm font-normal cursor-pointer"
				>
					处理完成后删除原图片
				</Label>
			</div>

			{isProcessing && (
				<div className="space-y-2 w-full">
					<div className="flex justify-between items-center">
						<Label>处理进度</Label>
						<span className="text-sm text-[#8C8C8C]">
							{processedCount} / {totalCount} 文件
						</span>
					</div>
					<Progress value={progress} className="h-2" />
				</div>
			)}

			<div className="pt-2">
				{!isProcessing ? (
					<Button
						onClick={onEnhance}
						disabled={disabled}
						className="w-full"
					>
						提升清晰度
					</Button>
				) : (
					<Button
						variant="destructive"
						onClick={onStop}
						className="w-full"
					>
						停止处理
					</Button>
				)}
			</div>
		</div>
	)
}
