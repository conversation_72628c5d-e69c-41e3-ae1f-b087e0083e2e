import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"

interface ImageUploadFormProps {
	settings: {
		platform: string
		token: string
		repository: string
		path: string
	}
	onSettingsChange: (settings: any) => void
	onUpload: () => void
	onStop?: () => void
	disabled: boolean
	isProcessing?: boolean
	progress?: number
	processedCount?: number
	totalCount?: number
}

export function ImageUploadForm({
	settings,
	onSettingsChange,
	onUpload,
	onStop,
	disabled,
	isProcessing = false,
	progress = 0,
	processedCount = 0,
	totalCount = 0
}: ImageUploadFormProps) {
	const platforms = [
		{ value: 'github', label: 'GitHub (免费图床)' },
		{ value: 'gitee', label: '码云 Gitee (国内访问快)' },
		{ value: 'custom', label: '自定义 (自托管图床)' }
	]

	return (
		<div className="space-y-4">
			<div className="space-y-4 w-full">
				<div className="space-y-2 w-full">
					<Label htmlFor="platform">图床平台</Label>
					<Select
						value={settings.platform}
						onValueChange={(value) => onSettingsChange({ platform: value })}
						disabled={isProcessing}
					>
						<SelectTrigger id="platform">
							<SelectValue placeholder="选择图床平台" />
						</SelectTrigger>
						<SelectContent>
							{platforms.map(platform => (
								<SelectItem key={platform.value} value={platform.value}>
									{platform.label}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>

				<div className="space-y-2 w-full">
					<Label htmlFor="token">访问令牌 (Token)</Label>
					<Input
						id="token"
						type="password"
						value={settings.token}
						onChange={(e) => onSettingsChange({ token: e.target.value })}
						placeholder="输入访问令牌"
						disabled={isProcessing}
						className="w-full"
					/>
				</div>

				<div className="space-y-2 w-full">
					<Label htmlFor="repository">仓库名称</Label>
					<Input
						id="repository"
						type="text"
						value={settings.repository}
						onChange={(e) => onSettingsChange({ repository: e.target.value })}
						placeholder="用户名/仓库名"
						disabled={isProcessing}
						className="w-full"
					/>
				</div>

				<div className="space-y-2 w-full">
					<Label htmlFor="path">存储路径</Label>
					<Input
						id="path"
						type="text"
						value={settings.path}
						onChange={(e) => onSettingsChange({ path: e.target.value })}
						placeholder="images/"
						disabled={isProcessing}
						className="w-full"
					/>
				</div>
			</div>

			{isProcessing && (
				<div className="space-y-2 w-full">
					<Progress value={progress} />
					{totalCount > 0 && (
						<div className="flex justify-between text-xs text-[#8C8C8C] mt-1">
							<span>上传进度</span>
							<span>{processedCount} / {totalCount} 文件</span>
						</div>
					)}
				</div>
			)}

			<div className="flex justify-end space-x-2 w-full">
				{isProcessing && onStop ? (
					<Button
						className="mt-4 w-full flex items-center justify-center gap-2 bg-red-500 hover:bg-red-600 text-white py-2 rounded-lg text-sm"
						onClick={onStop}
					>
						停止上传
					</Button>
				) : (
					<Button
						onClick={onUpload}
						disabled={disabled}
						className="w-full"
					>
						开始上传
					</Button>
				)}
			</div>
		</div>
	)
}
