import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Progress } from "@/components/ui/progress"
import { Slider } from "@/components/ui/slider"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ColorPicker } from "@/components/ui/color-picker"

interface ImageWatermarkFormProps {
	watermarkSettings: {
		type: 'text' | 'image'
		text: string
		textColor: string
		fontSize: number
		opacity: number
		position: string
		imagePath: string
		tiled: boolean
	}
	onSettingsChange: (settings: Partial<WatermarkSettings>) => void
	saveDirectory: string
	onSaveDirectoryChange?: (directory: string) => void
	deleteOriginal?: boolean
	onDeleteOriginalChange?: (deleteOriginal: boolean) => void
	onSelectDirectory: () => void
	onSelectWatermarkImage: () => void
	onApply: () => void
	disabled: boolean
	isProcessing?: boolean
	onStop?: () => void
	progress?: number
}

type WatermarkSettings = ImageWatermarkFormProps['watermarkSettings']

export function ImageWatermarkForm({
	watermarkSettings,
	onSettingsChange,
	saveDirectory,
	onSaveDirectoryChange,
	deleteOriginal = false,
	onDeleteOriginalChange,
	onSelectDirectory,
	onSelectWatermarkImage,
	onApply,
	disabled,
	isProcessing = false,
	onStop,
	progress = 0
}: ImageWatermarkFormProps) {
	const positions = [
		{ value: 'top-left', label: '左上角' },
		{ value: 'top-center', label: '顶部居中' },
		{ value: 'top-right', label: '右上角' },
		{ value: 'middle-left', label: '左侧居中' },
		{ value: 'middle-center', label: '正中心' },
		{ value: 'middle-right', label: '右侧居中' },
		{ value: 'bottom-left', label: '左下角' },
		{ value: 'bottom-center', label: '底部居中' },
		{ value: 'bottom-right', label: '右下角' },
	]

	return (
		<div className="space-y-4">
			<Tabs defaultValue="text" value={watermarkSettings.type} onValueChange={(value) => onSettingsChange({ type: value as 'text' | 'image' })} className="w-full">
				<TabsList className="grid w-full grid-cols-2">
					<TabsTrigger className="cursor-pointer" value="text" disabled={isProcessing}>文字水印</TabsTrigger>
					<TabsTrigger className="cursor-pointer" value="image" disabled={isProcessing}>图片水印</TabsTrigger>
				</TabsList>

				<TabsContent value="text" className="space-y-4 mt-4">
					<div className="space-y-2 w-full">
						<Label htmlFor="watermark-text">水印文字</Label>
						<Input
							id="watermark-text"
							value={watermarkSettings.text}
							onChange={(e) => onSettingsChange({ text: e.target.value })}
							placeholder="输入水印文字"
							disabled={isProcessing}
							className="w-full"
						/>
					</div>

					<div className="space-y-4 w-full">
						<div className="space-y-2 w-full">
							<Label htmlFor="text-color">文字颜色</Label>
							<div className="flex items-center space-x-2 w-full">
								<ColorPicker
									color={watermarkSettings.textColor}
									onChange={(color) => onSettingsChange({ textColor: color })}
									disabled={isProcessing}
								/>
								<Input
									id="text-color"
									value={watermarkSettings.textColor}
									onChange={(e) => onSettingsChange({ textColor: e.target.value })}
									className="flex-1"
									disabled={isProcessing}
								/>
							</div>
						</div>

						<div className="space-y-2 w-full">
							<Label htmlFor="font-size">字体大小</Label>
							<div className="flex items-center space-x-2 w-full">
								<Slider
									id="font-size"
									min={10}
									max={100}
									step={1}
									value={[watermarkSettings.fontSize]}
									onValueChange={(value) => onSettingsChange({ fontSize: value[0] })}
									disabled={isProcessing}
									className="flex-1"
								/>
								<span className="w-12 text-center">{watermarkSettings.fontSize}px</span>
							</div>
						</div>
					</div>
				</TabsContent>

				<TabsContent value="image" className="space-y-4 mt-4">
					<div className="space-y-2 w-full">
						<Label htmlFor="watermark-image">水印图片</Label>
						<div className="flex w-full">
							<Input
								id="watermark-image"
								value={watermarkSettings.imagePath}
								placeholder="选择水印图片"
								className="rounded-r-none"
								readOnly
								disabled={isProcessing}
							/>
							<Button
								variant="secondary"
								className="rounded-l-none"
								onClick={onSelectWatermarkImage}
								disabled={isProcessing}
							>
								浏览
							</Button>
						</div>
					</div>

					<div className="flex items-center space-x-2 w-full">
						<Checkbox
							id="tiled"
							checked={watermarkSettings.tiled}
							onCheckedChange={(checked) => onSettingsChange({ tiled: checked as boolean })}
							disabled={isProcessing}
						/>
						<Label
							htmlFor="tiled"
							className="text-sm font-normal cursor-pointer"
						>
							平铺水印（覆盖整个图片）
						</Label>
					</div>
				</TabsContent>
			</Tabs>

			<div className="space-y-2 w-full">
				<Label htmlFor="opacity">不透明度</Label>
				<div className="flex items-center space-x-2 w-full">
					<Slider
						id="opacity"
						min={1}
						max={100}
						step={1}
						value={[watermarkSettings.opacity]}
						onValueChange={(value) => onSettingsChange({ opacity: value[0] })}
						disabled={isProcessing}
						className="flex-1"
					/>
					<span className="w-12 text-center">{watermarkSettings.opacity}%</span>
				</div>
			</div>

			{!watermarkSettings.tiled && (
				<div className="space-y-2 w-full">
					<Label htmlFor="position">水印位置</Label>
					<Select
						value={watermarkSettings.position}
						onValueChange={(value) => onSettingsChange({ position: value })}
						disabled={isProcessing}
					>
						<SelectTrigger id="position">
							<SelectValue placeholder="选择水印位置" />
						</SelectTrigger>
						<SelectContent>
							{positions.map(position => (
								<SelectItem key={position.value} value={position.value}>
									{position.label}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>
			)}

			<div className="space-y-2 w-full">
				<Label htmlFor="save-directory">保存目录</Label>
				<div className="flex w-full">
					<Input
						id="save-directory"
						value={saveDirectory}
						placeholder="默认与原图片相同目录"
						className="rounded-r-none"
						readOnly
						disabled={isProcessing}
						onChange={(e) => onSaveDirectoryChange && onSaveDirectoryChange(e.target.value)}
					/>
					<Button
						variant="secondary"
						className="rounded-l-none"
						onClick={onSelectDirectory}
						disabled={isProcessing}
					>
						浏览
					</Button>
				</div>
			</div>

			<div className="flex items-center space-x-2 w-full">
				<Checkbox
					id="delete-original"
					checked={deleteOriginal}
					onCheckedChange={(checked) => onDeleteOriginalChange && onDeleteOriginalChange(checked as boolean)}
					disabled={isProcessing}
				/>
				<Label
					htmlFor="delete-original"
					className="text-sm font-normal cursor-pointer"
				>
					添加水印后删除原图片
				</Label>
			</div>

			{isProcessing && (
				<div className="space-y-2 w-full">
					<Progress value={progress} />
					<p className="text-xs text-[#8C8C8C] text-right">{Math.round(progress)}%</p>
				</div>
			)}

			<div className="flex justify-end space-x-2 w-full">
				{isProcessing && onStop ? (
					<Button
						className="mt-4 w-full flex items-center justify-center gap-2 bg-red-500 hover:bg-red-600 text-white py-2 rounded-lg text-sm"
						onClick={onStop}
					>
						停止处理
					</Button>
				) : (
					<Button
						onClick={onApply}
						disabled={disabled}
						className="w-full"
					>
						应用水印
					</Button>
				)}
			</div>
		</div>
	)
}
