import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Progress } from "@/components/ui/progress"
import { Checkbox } from "@/components/ui/checkbox"

interface ImageCompressFormProps {
	settings: {
		quality: number
		saveDirectory: string
		deleteOriginal?: boolean
	}
	onSettingsChange: (settings: any) => void
	onSelectDirectory: () => void
	onCompress: () => void
	onStop?: () => void
	disabled: boolean
	isProcessing: boolean
	progress?: number
	processedCount?: number
	totalCount?: number
}

export function ImageCompressForm({
	settings,
	onSettingsChange,
	onSelectDirectory,
	onCompress,
	onStop,
	disabled,
	isProcessing,
	progress = 0,
	processedCount = 0,
	totalCount = 0
}: ImageCompressFormProps) {
	return (
		<div className="space-y-4">
			<div className="space-y-2 w-full">
				<Label htmlFor="quality">压缩质量 ({settings.quality}%)</Label>
				<Slider
					id="quality"
					min={1}
					max={100}
					value={[settings.quality]}
					onValueChange={(value) => onSettingsChange({ quality: value[0] })}
					disabled={isProcessing}
				/>
				<div className="flex justify-between text-xs text-[#8C8C8C] mt-1">
					<span>低质量</span>
					<span>高质量</span>
				</div>
			</div>

			<div className="space-y-2 w-full">
				<Label htmlFor="save-directory">保存目录</Label>
				<div className="flex w-full">
					<Input
						id="save-directory"
						value={settings.saveDirectory}
						placeholder="默认与原图片相同目录"
						className="rounded-r-none"
						readOnly
						disabled={isProcessing}
					/>
					<Button
						variant="secondary"
						className="rounded-l-none"
						onClick={onSelectDirectory}
						disabled={isProcessing}
					>
						浏览
					</Button>
				</div>
			</div>

			<div className="flex items-center space-x-2 w-full">
				<Checkbox
					id="delete-original"
					checked={settings.deleteOriginal}
					onCheckedChange={(checked) => onSettingsChange({ deleteOriginal: checked })}
					disabled={isProcessing}
				/>
				<Label
					htmlFor="delete-original"
					className="text-sm font-normal cursor-pointer"
				>
					压缩完成后删除原图片
				</Label>
			</div>

			{isProcessing && (
				<div className="space-y-2 w-full">
					<div className="flex justify-between text-sm text-[#595959] mb-1">
						<span>当前进度</span>
						<span>{Math.round(progress)}%</span>
					</div>
					<Progress value={progress} />
					{totalCount > 0 && (
						<div className="flex justify-between text-xs text-[#8C8C8C] mt-1">
							<span>总进度</span>
							<span>{processedCount} / {totalCount} 文件</span>
						</div>
					)}
				</div>
			)}

			<div className="flex justify-end space-x-2 w-full">
				{isProcessing && onStop ? (
					<Button
						className="mt-4 w-full flex items-center justify-center gap-2 bg-red-500 hover:bg-red-600 text-white py-2 rounded-lg text-sm"
						onClick={onStop}
					>
						停止压缩
					</Button>
				) : (
					<Button
						onClick={onCompress}
						disabled={disabled}
						className="w-full"
					>
						开始压缩
					</Button>
				)}
			</div>
		</div>
	)
}
