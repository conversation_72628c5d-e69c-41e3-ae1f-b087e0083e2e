/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './pages/__root'
import { Route as IndexImport } from './pages/index'
import { Route as VideoTrimImport } from './pages/video/trim'
import { Route as VideoInfoImport } from './pages/video/info'
import { Route as VideoExtractAudioImport } from './pages/video/extract-audio'
import { Route as VideoConvertImport } from './pages/video/convert'
import { Route as VideoCompressImport } from './pages/video/compress'
import { Route as TextMarkdownImport } from './pages/text/markdown'
import { Route as TextFormatImport } from './pages/text/format'
import { Route as TextDiffImport } from './pages/text/diff'
import { Route as ImageWatermarkImport } from './pages/image/watermark'
import { Route as ImageUploadImport } from './pages/image/upload'
import { Route as ImageEnhanceImport } from './pages/image/enhance'
import { Route as ImageConvertImport } from './pages/image/convert'
import { Route as ImageCompressImport } from './pages/image/compress'
import { Route as DevUrlImport } from './pages/dev/url'
import { Route as DevRegexImport } from './pages/dev/regex'
import { Route as DevJsonImport } from './pages/dev/json'
import { Route as DevBase64Import } from './pages/dev/base64'

// Create/Update Routes

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const VideoTrimRoute = VideoTrimImport.update({
  id: '/video/trim',
  path: '/video/trim',
  getParentRoute: () => rootRoute,
} as any)

const VideoInfoRoute = VideoInfoImport.update({
  id: '/video/info',
  path: '/video/info',
  getParentRoute: () => rootRoute,
} as any)

const VideoExtractAudioRoute = VideoExtractAudioImport.update({
  id: '/video/extract-audio',
  path: '/video/extract-audio',
  getParentRoute: () => rootRoute,
} as any)

const VideoConvertRoute = VideoConvertImport.update({
  id: '/video/convert',
  path: '/video/convert',
  getParentRoute: () => rootRoute,
} as any)

const VideoCompressRoute = VideoCompressImport.update({
  id: '/video/compress',
  path: '/video/compress',
  getParentRoute: () => rootRoute,
} as any)

const TextMarkdownRoute = TextMarkdownImport.update({
  id: '/text/markdown',
  path: '/text/markdown',
  getParentRoute: () => rootRoute,
} as any)

const TextFormatRoute = TextFormatImport.update({
  id: '/text/format',
  path: '/text/format',
  getParentRoute: () => rootRoute,
} as any)

const TextDiffRoute = TextDiffImport.update({
  id: '/text/diff',
  path: '/text/diff',
  getParentRoute: () => rootRoute,
} as any)

const ImageWatermarkRoute = ImageWatermarkImport.update({
  id: '/image/watermark',
  path: '/image/watermark',
  getParentRoute: () => rootRoute,
} as any)

const ImageUploadRoute = ImageUploadImport.update({
  id: '/image/upload',
  path: '/image/upload',
  getParentRoute: () => rootRoute,
} as any)

const ImageEnhanceRoute = ImageEnhanceImport.update({
  id: '/image/enhance',
  path: '/image/enhance',
  getParentRoute: () => rootRoute,
} as any)

const ImageConvertRoute = ImageConvertImport.update({
  id: '/image/convert',
  path: '/image/convert',
  getParentRoute: () => rootRoute,
} as any)

const ImageCompressRoute = ImageCompressImport.update({
  id: '/image/compress',
  path: '/image/compress',
  getParentRoute: () => rootRoute,
} as any)

const DevUrlRoute = DevUrlImport.update({
  id: '/dev/url',
  path: '/dev/url',
  getParentRoute: () => rootRoute,
} as any)

const DevRegexRoute = DevRegexImport.update({
  id: '/dev/regex',
  path: '/dev/regex',
  getParentRoute: () => rootRoute,
} as any)

const DevJsonRoute = DevJsonImport.update({
  id: '/dev/json',
  path: '/dev/json',
  getParentRoute: () => rootRoute,
} as any)

const DevBase64Route = DevBase64Import.update({
  id: '/dev/base64',
  path: '/dev/base64',
  getParentRoute: () => rootRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/dev/base64': {
      id: '/dev/base64'
      path: '/dev/base64'
      fullPath: '/dev/base64'
      preLoaderRoute: typeof DevBase64Import
      parentRoute: typeof rootRoute
    }
    '/dev/json': {
      id: '/dev/json'
      path: '/dev/json'
      fullPath: '/dev/json'
      preLoaderRoute: typeof DevJsonImport
      parentRoute: typeof rootRoute
    }
    '/dev/regex': {
      id: '/dev/regex'
      path: '/dev/regex'
      fullPath: '/dev/regex'
      preLoaderRoute: typeof DevRegexImport
      parentRoute: typeof rootRoute
    }
    '/dev/url': {
      id: '/dev/url'
      path: '/dev/url'
      fullPath: '/dev/url'
      preLoaderRoute: typeof DevUrlImport
      parentRoute: typeof rootRoute
    }
    '/image/compress': {
      id: '/image/compress'
      path: '/image/compress'
      fullPath: '/image/compress'
      preLoaderRoute: typeof ImageCompressImport
      parentRoute: typeof rootRoute
    }
    '/image/convert': {
      id: '/image/convert'
      path: '/image/convert'
      fullPath: '/image/convert'
      preLoaderRoute: typeof ImageConvertImport
      parentRoute: typeof rootRoute
    }
    '/image/enhance': {
      id: '/image/enhance'
      path: '/image/enhance'
      fullPath: '/image/enhance'
      preLoaderRoute: typeof ImageEnhanceImport
      parentRoute: typeof rootRoute
    }
    '/image/upload': {
      id: '/image/upload'
      path: '/image/upload'
      fullPath: '/image/upload'
      preLoaderRoute: typeof ImageUploadImport
      parentRoute: typeof rootRoute
    }
    '/image/watermark': {
      id: '/image/watermark'
      path: '/image/watermark'
      fullPath: '/image/watermark'
      preLoaderRoute: typeof ImageWatermarkImport
      parentRoute: typeof rootRoute
    }
    '/text/diff': {
      id: '/text/diff'
      path: '/text/diff'
      fullPath: '/text/diff'
      preLoaderRoute: typeof TextDiffImport
      parentRoute: typeof rootRoute
    }
    '/text/format': {
      id: '/text/format'
      path: '/text/format'
      fullPath: '/text/format'
      preLoaderRoute: typeof TextFormatImport
      parentRoute: typeof rootRoute
    }
    '/text/markdown': {
      id: '/text/markdown'
      path: '/text/markdown'
      fullPath: '/text/markdown'
      preLoaderRoute: typeof TextMarkdownImport
      parentRoute: typeof rootRoute
    }
    '/video/compress': {
      id: '/video/compress'
      path: '/video/compress'
      fullPath: '/video/compress'
      preLoaderRoute: typeof VideoCompressImport
      parentRoute: typeof rootRoute
    }
    '/video/convert': {
      id: '/video/convert'
      path: '/video/convert'
      fullPath: '/video/convert'
      preLoaderRoute: typeof VideoConvertImport
      parentRoute: typeof rootRoute
    }
    '/video/extract-audio': {
      id: '/video/extract-audio'
      path: '/video/extract-audio'
      fullPath: '/video/extract-audio'
      preLoaderRoute: typeof VideoExtractAudioImport
      parentRoute: typeof rootRoute
    }
    '/video/info': {
      id: '/video/info'
      path: '/video/info'
      fullPath: '/video/info'
      preLoaderRoute: typeof VideoInfoImport
      parentRoute: typeof rootRoute
    }
    '/video/trim': {
      id: '/video/trim'
      path: '/video/trim'
      fullPath: '/video/trim'
      preLoaderRoute: typeof VideoTrimImport
      parentRoute: typeof rootRoute
    }
  }
}

// Create and export the route tree

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/dev/base64': typeof DevBase64Route
  '/dev/json': typeof DevJsonRoute
  '/dev/regex': typeof DevRegexRoute
  '/dev/url': typeof DevUrlRoute
  '/image/compress': typeof ImageCompressRoute
  '/image/convert': typeof ImageConvertRoute
  '/image/enhance': typeof ImageEnhanceRoute
  '/image/upload': typeof ImageUploadRoute
  '/image/watermark': typeof ImageWatermarkRoute
  '/text/diff': typeof TextDiffRoute
  '/text/format': typeof TextFormatRoute
  '/text/markdown': typeof TextMarkdownRoute
  '/video/compress': typeof VideoCompressRoute
  '/video/convert': typeof VideoConvertRoute
  '/video/extract-audio': typeof VideoExtractAudioRoute
  '/video/info': typeof VideoInfoRoute
  '/video/trim': typeof VideoTrimRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/dev/base64': typeof DevBase64Route
  '/dev/json': typeof DevJsonRoute
  '/dev/regex': typeof DevRegexRoute
  '/dev/url': typeof DevUrlRoute
  '/image/compress': typeof ImageCompressRoute
  '/image/convert': typeof ImageConvertRoute
  '/image/enhance': typeof ImageEnhanceRoute
  '/image/upload': typeof ImageUploadRoute
  '/image/watermark': typeof ImageWatermarkRoute
  '/text/diff': typeof TextDiffRoute
  '/text/format': typeof TextFormatRoute
  '/text/markdown': typeof TextMarkdownRoute
  '/video/compress': typeof VideoCompressRoute
  '/video/convert': typeof VideoConvertRoute
  '/video/extract-audio': typeof VideoExtractAudioRoute
  '/video/info': typeof VideoInfoRoute
  '/video/trim': typeof VideoTrimRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/dev/base64': typeof DevBase64Route
  '/dev/json': typeof DevJsonRoute
  '/dev/regex': typeof DevRegexRoute
  '/dev/url': typeof DevUrlRoute
  '/image/compress': typeof ImageCompressRoute
  '/image/convert': typeof ImageConvertRoute
  '/image/enhance': typeof ImageEnhanceRoute
  '/image/upload': typeof ImageUploadRoute
  '/image/watermark': typeof ImageWatermarkRoute
  '/text/diff': typeof TextDiffRoute
  '/text/format': typeof TextFormatRoute
  '/text/markdown': typeof TextMarkdownRoute
  '/video/compress': typeof VideoCompressRoute
  '/video/convert': typeof VideoConvertRoute
  '/video/extract-audio': typeof VideoExtractAudioRoute
  '/video/info': typeof VideoInfoRoute
  '/video/trim': typeof VideoTrimRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/dev/base64'
    | '/dev/json'
    | '/dev/regex'
    | '/dev/url'
    | '/image/compress'
    | '/image/convert'
    | '/image/enhance'
    | '/image/upload'
    | '/image/watermark'
    | '/text/diff'
    | '/text/format'
    | '/text/markdown'
    | '/video/compress'
    | '/video/convert'
    | '/video/extract-audio'
    | '/video/info'
    | '/video/trim'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/dev/base64'
    | '/dev/json'
    | '/dev/regex'
    | '/dev/url'
    | '/image/compress'
    | '/image/convert'
    | '/image/enhance'
    | '/image/upload'
    | '/image/watermark'
    | '/text/diff'
    | '/text/format'
    | '/text/markdown'
    | '/video/compress'
    | '/video/convert'
    | '/video/extract-audio'
    | '/video/info'
    | '/video/trim'
  id:
    | '__root__'
    | '/'
    | '/dev/base64'
    | '/dev/json'
    | '/dev/regex'
    | '/dev/url'
    | '/image/compress'
    | '/image/convert'
    | '/image/enhance'
    | '/image/upload'
    | '/image/watermark'
    | '/text/diff'
    | '/text/format'
    | '/text/markdown'
    | '/video/compress'
    | '/video/convert'
    | '/video/extract-audio'
    | '/video/info'
    | '/video/trim'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  DevBase64Route: typeof DevBase64Route
  DevJsonRoute: typeof DevJsonRoute
  DevRegexRoute: typeof DevRegexRoute
  DevUrlRoute: typeof DevUrlRoute
  ImageCompressRoute: typeof ImageCompressRoute
  ImageConvertRoute: typeof ImageConvertRoute
  ImageEnhanceRoute: typeof ImageEnhanceRoute
  ImageUploadRoute: typeof ImageUploadRoute
  ImageWatermarkRoute: typeof ImageWatermarkRoute
  TextDiffRoute: typeof TextDiffRoute
  TextFormatRoute: typeof TextFormatRoute
  TextMarkdownRoute: typeof TextMarkdownRoute
  VideoCompressRoute: typeof VideoCompressRoute
  VideoConvertRoute: typeof VideoConvertRoute
  VideoExtractAudioRoute: typeof VideoExtractAudioRoute
  VideoInfoRoute: typeof VideoInfoRoute
  VideoTrimRoute: typeof VideoTrimRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  DevBase64Route: DevBase64Route,
  DevJsonRoute: DevJsonRoute,
  DevRegexRoute: DevRegexRoute,
  DevUrlRoute: DevUrlRoute,
  ImageCompressRoute: ImageCompressRoute,
  ImageConvertRoute: ImageConvertRoute,
  ImageEnhanceRoute: ImageEnhanceRoute,
  ImageUploadRoute: ImageUploadRoute,
  ImageWatermarkRoute: ImageWatermarkRoute,
  TextDiffRoute: TextDiffRoute,
  TextFormatRoute: TextFormatRoute,
  TextMarkdownRoute: TextMarkdownRoute,
  VideoCompressRoute: VideoCompressRoute,
  VideoConvertRoute: VideoConvertRoute,
  VideoExtractAudioRoute: VideoExtractAudioRoute,
  VideoInfoRoute: VideoInfoRoute,
  VideoTrimRoute: VideoTrimRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/dev/base64",
        "/dev/json",
        "/dev/regex",
        "/dev/url",
        "/image/compress",
        "/image/convert",
        "/image/enhance",
        "/image/upload",
        "/image/watermark",
        "/text/diff",
        "/text/format",
        "/text/markdown",
        "/video/compress",
        "/video/convert",
        "/video/extract-audio",
        "/video/info",
        "/video/trim"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/dev/base64": {
      "filePath": "dev/base64.tsx"
    },
    "/dev/json": {
      "filePath": "dev/json.tsx"
    },
    "/dev/regex": {
      "filePath": "dev/regex.tsx"
    },
    "/dev/url": {
      "filePath": "dev/url.tsx"
    },
    "/image/compress": {
      "filePath": "image/compress.tsx"
    },
    "/image/convert": {
      "filePath": "image/convert.tsx"
    },
    "/image/enhance": {
      "filePath": "image/enhance.tsx"
    },
    "/image/upload": {
      "filePath": "image/upload.tsx"
    },
    "/image/watermark": {
      "filePath": "image/watermark.tsx"
    },
    "/text/diff": {
      "filePath": "text/diff.tsx"
    },
    "/text/format": {
      "filePath": "text/format.tsx"
    },
    "/text/markdown": {
      "filePath": "text/markdown.tsx"
    },
    "/video/compress": {
      "filePath": "video/compress.tsx"
    },
    "/video/convert": {
      "filePath": "video/convert.tsx"
    },
    "/video/extract-audio": {
      "filePath": "video/extract-audio.tsx"
    },
    "/video/info": {
      "filePath": "video/info.tsx"
    },
    "/video/trim": {
      "filePath": "video/trim.tsx"
    }
  }
}
ROUTE_MANIFEST_END */
