{"name": "media-tool", "version": "1.0.0", "description": "An Electron application with React and TypeScript", "main": "./out/main/index.js", "author": "example.com", "homepage": "https://electron-vite.org", "scripts": {"format": "prettier --write .", "lint": "eslint --cache .", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "npm run typecheck && electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "electron-vite build && electron-builder --mac", "build:linux": "electron-vite build && electron-builder --linux"}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^4.0.0", "@ffmpeg-installer/ffmpeg": "^1.1.0", "@ffprobe-installer/ffprobe": "^2.1.2", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@tailwindcss/vite": "^4.0.8", "@tanstack/react-router": "^1.111.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "electron-updater": "^6.3.9", "fluent-ffmpeg": "^2.1.3", "fs-extra": "^11.3.0", "log4js": "^6.9.1", "lucide-react": "^0.476.0", "path-browserify-esm": "^1.0.6", "sharp": "^0.34.1", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.0.8", "tw-animate-css": "^1.2.5", "zustand": "^5.0.3"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "^3.0.0", "@electron-toolkit/eslint-config-ts": "^3.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@tanstack/router-devtools": "^1.111.7", "@tanstack/router-plugin": "^1.111.7", "@types/fluent-ffmpeg": "^2.1.27", "@types/fs-extra": "^11.0.4", "@types/node": "^22.13.4", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "daisyui": "5.0.0-beta.8", "electron": "^34.2.0", "electron-builder": "^25.1.8", "electron-vite": "^3.0.0", "eslint": "^9.20.1", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "prettier": "^3.5.1", "react": "^18.3.1", "react-dom": "^18.3.1", "typescript": "^5.7.3", "vite": "^6.1.0"}, "packageManager": "pnpm@9.13.0+sha512.beb9e2a803db336c10c9af682b58ad7181ca0fbd0d4119f2b33d5f2582e96d6c0d93c85b23869295b765170fbdaa92890c0da6ada457415039769edf3c959efe"}